"use client"

import { createContext, startTransition, useEffect, useState } from "react"

import { sessionRefreshAction, verifySessionAction } from "@/lib/actions"
import { UserSession } from "@/lib/auth"

export const SessionContext = createContext<UserSession | undefined | null>(null)

export function SessionProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<UserSession | undefined | null>(null)

  useEffect(() => {
    startTransition(async () => {
      const res = await verifySessionAction()
      startTransition(() => setSession(res))
      await sessionRefreshAction()
    })
  }, [])

  return <SessionContext value={session}>{children}</SessionContext>
}
