import { PrismaClient } from '@prisma/client';
import { faker } from '@faker-js/faker';

const prisma = new PrismaClient();

// إعداد Faker للغة العربية
faker.locale = 'ar';

// مواضيع متنوعة للمحتوى
const topics = [
  'العلاج النفسي',
  'الصحة النفسية',
  'التطوير الذاتي',
  'العلاقات الإنسانية',
  'إدارة الضغوط',
  'القلق والاكتئاب',
  'الثقة بالنفس',
  'التأمل والاسترخاء',
  'العلاج السلوكي المعرفي',
  'الذكاء العاطفي',
  'التواصل الفعال',
  'حل المشاكل',
  'الإبداع والابتكار',
  'إدارة الوقت',
  'التحفيز الذاتي',
  'العلاج الأسري',
  'علم النفس الإيجابي',
  'اضطرابات الشخصية',
  'العلاج الجماعي',
  'الصدمات النفسية'
];

// كلمات مفتاحية متنوعة
const keywords = [
  'علاج نفسي',
  'صحة نفسية',
  'تطوير ذاتي',
  'علاقات',
  'ضغوط',
  'قلق',
  'اكتئاب',
  'ثقة',
  'تأمل',
  'استرخاء',
  'سلوكي',
  'معرفي',
  'ذكاء عاطفي',
  'تواصل',
  'حلول',
  'إبداع',
  'وقت',
  'تحفيز',
  'نجاح',
  'سعادة',
  'علاج أسري',
  'إيجابية',
  'شخصية',
  'جماعي',
  'صدمة'
];

// عناوين المقالات
const articleTitles = [
  'كيفية التعامل مع القلق في العصر الحديث',
  'أسرار بناء الثقة بالنفس',
  'دليلك الشامل للعلاج النفسي',
  'خطوات عملية لتحسين الصحة النفسية',
  'أهمية التأمل في حياتنا اليومية',
  'تقنيات فعالة لإدارة الضغوط',
  'أخطاء شائعة في العلاقات وكيفية تجنبها',
  'رحلة الشفاء من الاكتئاب',
  'علامات تدل على ضرورة طلب المساعدة النفسية',
  'نصائح الخبراء حول العلاج السلوكي المعرفي'
];

// عناوين الكتب
const bookTitles = [
  'دليل العلاج النفسي الحديث',
  'فن إدارة الضغوط النفسية',
  'رحلة إلى الذات: كتاب التطوير الشخصي',
  'العلاقات الصحية: دليل عملي',
  'التغلب على القلق والخوف',
  'الذكاء العاطفي في الحياة العملية',
  'أسرار السعادة النفسية',
  'العلاج الأسري: نظريات وتطبيقات',
  'فهم الاكتئاب وعلاجه',
  'بناء الثقة بالنفس خطوة بخطوة'
];

// عناوين المقابلات
const interviewTitles = [
  'مقابلة مع خبير العلاج النفسي حول القلق',
  'حوار شامل عن الصحة النفسية في المجتمع',
  'لقاء مع متخصص في العلاج الأسري',
  'مناقشة عميقة حول الاكتئاب وعلاجه',
  'حديث مع خبير التطوير الذاتي',
  'مقابلة حول أهمية الذكاء العاطفي',
  'لقاء مع متخصص في علاج الصدمات',
  'حوار عن العلاج السلوكي المعرفي',
  'مقابلة حول التأمل والاسترخاء',
  'لقاء مع خبير العلاقات الإنسانية'
];

// روابط فيديو YouTube وهمية
const youtubeUrls = [
  'https://www.youtube.com/embed/HTMDNZOlUq4?si=xck9Tafeb8in9zgy',
  'https://www.youtube.com/embed/dQw4w9WgXcQ?si=abc123def456',
  'https://www.youtube.com/embed/9bZkp7q19f0?si=xyz789uvw012',
  'https://www.youtube.com/embed/kJQP7kiw5Fk?si=mno345pqr678',
  'https://www.youtube.com/embed/L_jWHffIx5E?si=stu901vwx234',
  'https://www.youtube.com/embed/fJ9rUzIMcZQ?si=yza567bcd890',
  'https://www.youtube.com/embed/ZZ5LpwO-An4?si=efg123hij456',
  'https://www.youtube.com/embed/hFZFjoX2cGg?si=klm789nop012',
  'https://www.youtube.com/embed/y6120QOlsfU?si=qrs345tuv678',
  'https://www.youtube.com/embed/djV11Xbc914?si=wxy901zab234'
];

// صور أغلفة الكتب
const bookCovers = [
  'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=600',
  'https://images.unsplash.com/photo-1544716278-ca5e3f4abd8c?w=400&h=600',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=600',
  'https://images.unsplash.com/photo-1516979187457-637abb4f9353?w=400&h=600',
  'https://images.unsplash.com/photo-1495446815901-a7297e633e8d?w=400&h=600',
  'https://images.unsplash.com/photo-1524995997946-a1c2e315a42f?w=400&h=600',
  'https://images.unsplash.com/photo-1543002588-bfa74002ed7e?w=400&h=600',
  'https://images.unsplash.com/photo-1519904981063-b0cf448d479e?w=400&h=600',
  'https://images.unsplash.com/photo-1532012197267-da84d127e765?w=400&h=600',
  'https://images.unsplash.com/photo-1550399105-c4db5fb85c18?w=400&h=600'
];

// صور مصغرة للمقابلات
const thumbnails = [
  'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=800&h=450',
  'https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?w=800&h=450',
  'https://images.unsplash.com/photo-1586297135537-94bc9ba060aa?w=800&h=450',
  'https://images.unsplash.com/photo-1551836022-deb4988cc6c0?w=800&h=450',
  'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=450',
  'https://images.unsplash.com/photo-1573497019940-1c28c88b4f3e?w=800&h=450',
  'https://images.unsplash.com/photo-1552058544-f2b08422138a?w=800&h=450',
  'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=800&h=450',
  'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=800&h=450',
  'https://images.unsplash.com/photo-1544027993-37dbfe43562a?w=800&h=450'
];

function getRandomElement(array) {
  return array[Math.floor(Math.random() * array.length)];
}

function getRandomElements(array, count) {
  const shuffled = array.sort(() => 0.5 - Math.random());
  return shuffled.slice(0, count);
}

function generateKeywords() {
  const count = Math.floor(Math.random() * 5) + 3; // 3-7 كلمات مفتاحية
  return getRandomElements(keywords, count);
}

function generateSeoDescription(title) {
  const descriptions = [
    `اكتشف كل ما تحتاج لمعرفته حول ${title}. نصائح عملية ومفيدة من خبراء العلاج النفسي.`,
    `دليل شامل حول ${title} مع أحدث الأبحاث والتقنيات المثبتة علمياً.`,
    `تعلم كيفية تطبيق مبادئ ${title} في حياتك اليومية لتحقيق نتائج أفضل.`,
    `محتوى متخصص يتناول ${title} بأسلوب علمي مبسط ونصائح عملية.`
  ];
  return getRandomElement(descriptions);
}

function generateRandomDate() {
  return faker.date.between({
    from: new Date(Date.now() - 365 * 24 * 60 * 60 * 1000),
    to: new Date()
  });
}

// دالة إنشاء المقالات
async function createArticles() {
  console.log('بدء إنشاء 50 مقال وهمي...');
  
  const articles = [];
  
  for (let i = 0; i < 50; i++) {
    const title = getRandomElement(articleTitles) + ` - الجزء ${i + 1}`;
    const topic = getRandomElement(topics);
    
    const article = `في عالمنا المعاصر، أصبح موضوع ${topic} من أهم المواضيع التي تشغل بال الكثيرين.

إن فهم ${topic} بشكل صحيح يتطلب منا النظر إليه من زوايا متعددة. فمن ناحية، نجد أن الأبحاث الحديثة تشير إلى أهمية كبيرة لهذا الموضوع في تحسين جودة الحياة.

من خلال تجربتي المهنية، لاحظت أن الأشخاص الذين يولون اهتماماً خاصاً بـ ${topic} يحققون نتائج أفضل في حياتهم الشخصية والمهنية.

هناك عدة استراتيجيات يمكن اتباعها:
• التركيز على الجوانب الإيجابية
• وضع أهداف واقعية وقابلة للتحقيق
• البحث عن الدعم المناسب عند الحاجة
• الممارسة المستمرة والصبر

في النهاية، أود أن أؤكد على أن رحلة التحسن في ${topic} تحتاج إلى وقت وجهد، لكن النتائج تستحق العناء.

من المهم أيضاً أن نتذكر أن كل شخص فريد في تجربته، وما يناسب شخصاً قد لا يناسب آخر. لذلك، من الضروري العمل مع متخصص مؤهل لوضع خطة علاجية مناسبة.`;

    articles.push({
      title,
      article,
      seoDescription: generateSeoDescription(title),
      seokeywords: generateKeywords(),
      createdAt: generateRandomDate()
    });
    
    if ((i + 1) % 10 === 0) {
      console.log(`تم إنشاء ${i + 1} مقال...`);
    }
  }
  
  await prisma.article.createMany({ data: articles });
  console.log('✅ تم إنشاء 50 مقال وهمي بنجاح!');
}

// دالة إنشاء الكتب
async function createBooks() {
  console.log('بدء إنشاء 50 كتاب وهمي...');
  
  const books = [];
  
  for (let i = 0; i < 50; i++) {
    const title = getRandomElement(bookTitles) + ` - الإصدار ${i + 1}`;
    const topic = getRandomElement(topics);
    
    const summary = `هذا الكتاب يتناول موضوع ${topic} بشكل شامل ومفصل. يقدم الكتاب نظرة عميقة على أحدث الأبحاث والتقنيات في هذا المجال، مع تطبيقات عملية يمكن للقارئ الاستفادة منها في حياته اليومية. يحتوي الكتاب على دراسات حالة واقعية وتمارين تطبيقية تساعد على فهم المفاهيم بشكل أفضل.`;
    
    books.push({
      title,
      summary,
      coverImage: getRandomElement(bookCovers),
      pagesCount: Math.floor(Math.random() * 300) + 100, // 100-400 صفحة
      price: Math.floor(Math.random() * 50) + 10, // 10-60 دولار
      bookUrl: `https://example.com/books/book-${i + 1}.pdf`,
      seoDescription: generateSeoDescription(title),
      seokeywords: generateKeywords(),
      createdAt: generateRandomDate()
    });
    
    if ((i + 1) % 10 === 0) {
      console.log(`تم إنشاء ${i + 1} كتاب...`);
    }
  }
  
  await prisma.book.createMany({ data: books });
  console.log('✅ تم إنشاء 50 كتاب وهمي بنجاح!');
}

// دالة إنشاء المقابلات
async function createInterviews() {
  console.log('بدء إنشاء 50 مقابلة وهمية...');
  
  const interviews = [];
  
  for (let i = 0; i < 50; i++) {
    const title = getRandomElement(interviewTitles) + ` - الحلقة ${i + 1}`;
    const topic = getRandomElement(topics);
    
    const description = `في هذه المقابلة الشيقة، نناقش موضوع ${topic} مع أحد الخبراء المتخصصين في هذا المجال. تتضمن المقابلة نصائح عملية وإجابات على الأسئلة الأكثر شيوعاً، بالإضافة إلى تجارب واقعية من الممارسة المهنية. هذه المقابلة مفيدة للمهتمين بفهم ${topic} بشكل أعمق والاستفادة من خبرة المتخصصين.`;
    
    interviews.push({
      title,
      description,
      videoUrl: getRandomElement(youtubeUrls),
      thumbnail: getRandomElement(thumbnails),
      seoDescription: generateSeoDescription(title),
      seokeywords: generateKeywords(),
      createdAt: generateRandomDate()
    });
    
    if ((i + 1) % 10 === 0) {
      console.log(`تم إنشاء ${i + 1} مقابلة...`);
    }
  }
  
  await prisma.interview.createMany({ data: interviews });
  console.log('✅ تم إنشاء 50 مقابلة وهمية بنجاح!');
}

// الدالة الرئيسية
async function seedContentData() {
  try {
    console.log('🌱 بدء إنشاء البيانات التجريبية...');
    
    await createArticles();
    await createBooks();
    await createInterviews();
    
    // عرض الإحصائيات النهائية
    const totalArticles = await prisma.article.count();
    const totalBooks = await prisma.book.count();
    const totalInterviews = await prisma.interview.count();
    
    console.log('\n📊 الإحصائيات النهائية:');
    console.log(`📝 إجمالي المقالات: ${totalArticles}`);
    console.log(`📚 إجمالي الكتب: ${totalBooks}`);
    console.log(`🎥 إجمالي المقابلات: ${totalInterviews}`);
    console.log('\n✅ تم إنشاء جميع البيانات التجريبية بنجاح!');
    
  } catch (error) {
    console.error('❌ خطأ في إنشاء البيانات:', error);
  } finally {
    await prisma.$disconnect();
  }
}

// تشغيل السكريبت
seedContentData();
