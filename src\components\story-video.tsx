"use client"

import React from "react"
import { SiteSettings } from "@prisma/client"
import { Share2 } from "lucide-react"

import { cn } from "@/lib/utils"

import { Button } from "./ui/button"

export function StoryVideo({
  siteSettings,
}: {
  siteSettings: Promise<SiteSettings>
}) {
  const { myStoryVideoUrl, myStoryVideoThumbnailUrl } = React.use(siteSettings)
  const [isPlaying, setIsPlaying] = React.useState(false)

  return (
    <div className="relative h-full w-full">
      <div
        aria-hidden
        className={cn(
          "pointer-events-none absolute top-0 right-0 z-10 flex h-16 w-full rounded-xl bg-gradient-to-b from-black/80 to-black/0 px-3 py-2 transition-all transition-discrete duration-300",
          isPlaying ? "opacity-0" : "opacity-100"
        )}
      >
        <span className="text-background w-full truncate text-xs drop-shadow-[0_0_1px_#000000] sm:text-sm">
          قصتي مع العلاج الشعوري
        </span>
      </div>
      <video
        onPlay={() => setIsPlaying(true)}
        onPause={() => setIsPlaying(false)}
        title="قصتي مع العلاج الشعوري"
        id="myStoryVideo"
        className="max-h-full max-w-full scroll-mt-32 rounded-xl border-0 drop-shadow-[0_0px_6px_#00000099]"
        width="636"
        height="392"
        controls
        preload="none"
        autoPlay={false}
        // poster="https://pub-431af6a83a2349a29a6c160751fe1f9e.r2.dev/site-settings/images/لقطة-الشاشة-2025-05-18-175600-82222.png"
        src={myStoryVideoUrl || undefined}
        poster={myStoryVideoThumbnailUrl || undefined}
      >
        <source src={myStoryVideoUrl || undefined} type="video/mp4" />
        لا يدعم متصفحك تشغيل الفيديو.
      </video>
    </div>
  )
}

export function StoryShareVideo() {
  const domain = process.env.NEXT_PUBLIC_DOMAIN
  return (
    <div className="mt-4 w-full">
      <Button
        variant={"outlineDark"}
        className="w-full"
        onClick={async () => {
          if (navigator.share) {
            try {
              await navigator.share({
                url: `${domain}#myStoryVideo`,
              })
              // eslint-disable-next-line
            } catch (error) {
              console.error("خطأ أثناء المشاركة")
            }
          } else {
            alert("المشاركة غير مدعومة على هذا المتصفح.")
          }
        }}
      >
        مشاركة الفيديو <Share2 />
      </Button>
    </div>
  )
}
