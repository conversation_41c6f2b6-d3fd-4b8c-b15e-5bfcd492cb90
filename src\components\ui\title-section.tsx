import * as React from "react"

import { cn } from "@/lib/utils"

export default function TitleSection({
  title,
  description,
  classTitle,
  classDescription,
  classContainer,
}: {
  title: React.JSX.Element
  description?: string
  classTitle?: string
  classDescription?: string
  classContainer?: string
}) {
  return (
    <div
      className={cn(
        "mx-auto flex max-w-md flex-col items-center gap-2 text-center md:max-w-3xl md:gap-3 lg:gap-4",
        classContainer
      )}
    >
      <div
        className={cn(
          "bg-primary-gradient-x max-w-fit bg-clip-text py-2 text-4xl font-bold text-balance text-transparent drop-shadow-[0_4px_2px_#00000050] md:text-5xl lg:text-6xl",
          classTitle
        )}
      >
        {title}
      </div>
      {description && (
        <p
          className={cn(
            "px-3 text-sm leading-relaxed text-balance md:text-base md:leading-relaxed",
            classDescription
          )}
        >
          {description}
        </p>
      )}
    </div>
  )
}
