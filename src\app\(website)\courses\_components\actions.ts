"use server"

import { redirect } from "next/navigation"

import { createSession, verifySession } from "@/lib/auth"
import prisma from "@/lib/prisma"

// ==================================================================================
// دالة خادم متخصصة بالتعامل مع نقر المستخدم على زر اشتراك في الدورة
// ==================================================================================
export async function subscribeCourseAction({
  courseId,
}: {
  courseId: string | undefined
}): Promise<{
  success: boolean
  message: string
}> {
  const session = await verifySession()

  // const userCourseOrders = await prisma.

  // اذا كان المستخدم لم يسجل الدخول
  if (!session || !courseId) {
    const callbackUrl = encodeURIComponent(`/courses/${courseId}`)
    redirect(`/auth/login?callback-url=${callbackUrl}`)
  }

  const isSubscribed = session.purchasesIds.includes(courseId)

  if (isSubscribed) {
    return { success: true, message: "لقد قمت بالاشتراك في هذا الدورة من قبل" }
  }

  // التأكد من وجود الدورة في قاعدة البيانات وجلب بياناته
  const course = await prisma.course.findUnique({
    where: { id: courseId },
    select: { price: true },
  })

  if (!course) {
    return {
      success: false,
      message: "حدث خطأ أثناء معالجة البيانات او ربما قد تم حذف هذا الدورة",
    }
  }

  if (course.price === 0) {
    const newOrder = await prisma.courseOrder.create({
      data: {
        status: "PAID",
        price: course.price,
        course: { connect: { id: courseId } },
        user: { connect: { id: session.id } },
      },
    })
    await createSession({
      ...session,
      purchasesIds: [...session.purchasesIds, newOrder.courseId],
    })

    return {
      success: true,
      message: "تم الإشتراك في الدورة بنجاح",
    }
  }

  if (course.price > 0) {
    return {
      success: false,
      message: "دورة مدفوع ستم التعامل مع طريقة الشراء لاحقًا",
    }
  }

  return { success: false, message: "خطأ غير معروف !!" }
}
