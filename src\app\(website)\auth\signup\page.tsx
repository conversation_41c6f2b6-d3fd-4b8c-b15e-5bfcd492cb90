import { SignupForm } from "@/app/(website)/auth/signup/signup-form";
import { siteName } from "@/configs/site";
import { Metadata } from "next";
import { Suspense } from "react";

export const metadata: Metadata = {
  title: `إنشاء حساب جديد | ${siteName}`,
  description: "إنشاء حساب جديد في موقع اكاديمية د. ناهد باشطح او قم بتسجيل الدخول",
  keywords: [
    "تسجيل",
    "الدخول",
    "اشتراك",
    "إنشاء حساب",
    "تسجيل الدخول",
    "إنشاء حساب جديد",
    "اكاديمية د. ناهد باشطح",
  ],
};

export default function SignupPage() {
  return (
    <div className="flex w-full items-center justify-center">
      <Suspense fallback={<div>loading...</div>}>
        <SignupForm />
      </Suspense>
    </div>
  );
}
