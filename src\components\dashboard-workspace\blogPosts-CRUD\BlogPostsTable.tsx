"use client"

import { formatDate } from "@/utils/formaters"
import { BlogPost } from "@prisma/client"

import DataTable from "@/components/dashboard/components/table/DataTable"
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types"

import PageLayout from "../PageLayout"
import TableArray from "../TableArray"
import TableImage from "../TableImage"

export default function BlogPostsTable({
  data,
  onDelete,
}: {
  data: BlogPost[]
  onDelete?: DataTableOnDeleteFnDef<BlogPost>
}) {
  return (
    <PageLayout title="منشورات المدونه">
      <DataTable<BlogPost>
        data={data}
        defaultPageSize={5}
        columnSearch={{ columnKey: "title" }}
        createDataButton={{
          href: "/admin/blogPosts/create",
          label: "إضافة منشور",
        }}
        rowActions={{
          onDelete,
          links: {
            items: [{ basePath: "/admin/blogPosts/update", label: "تعديل" }],
          },
        }}
        createColumns={[
          { accessorKey: "title", columnLabel: "عنوان المنشور" },
          {
            accessorKey: "image",
            columnLabel: "صورة المنشور",
            cell: ({ value }) => <TableImage src={value} />,
          },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) => formatDate(value),
          },
          {
            accessorKey: "content",
            columnLabel: "محتوى المنشور",
            cell: ({ value }) => (
              <div className="max-w-[200px] overflow-hidden text-ellipsis">
                {value}
              </div>
            ),
          },
          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },
          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  )
}
