import ArticleForm from "@/components/dashboard-workspace/articles-CRUD/ArticlesForm";
import { articlesSchema } from "@/components/dashboard-workspace/articles-CRUD/ArticlesSchema";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import prisma from "@/lib/prisma";
import { dataLimits } from "@/configs/site";
import { revalidatePath, revalidateTag } from "next/cache";
import { notFound } from "next/navigation";
import React from "react";

export default async function ArticleUpdatePage(props: { params: Promise<{ articleId: string }> }) {
  const { articleId } = await props.params;
  const article = await prisma.article.findUnique({ where: { id: articleId } });
  if (!article) return notFound();

  return (
    <PageLayout title="تعديل المقال">
      <ArticleForm
        mode="update"
        defaultValues={article}
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = articlesSchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const [updatedArticle, articlesBefore] = await prisma.$transaction([
            prisma.article.update({
              where: { id: data.id },
              data,
            }),

            prisma.article.count({
              where: {
                createdAt: {
                  gt: article.createdAt,
                },
              },
            }),
          ]);

          const pageIndex = Math.floor(articlesBefore / dataLimits.articles) + 1;

          revalidateTag("pageTitlesAndIds");
          revalidatePath("/admin/articles");
          revalidatePath(`/media-experience/articles/${pageIndex}`);
          revalidatePath(`/media-experience/articles/${pageIndex}/${updatedArticle.id}`);

          return { success: true, message: "تم تعديل المقال بنجاح" };
        }}
      />
    </PageLayout>
  );
}
