import { lectureSchema } from "@/components/dashboard-workspace/lectures-CRUD/lectureSchema";
import LecturesForm from "@/components/dashboard-workspace/lectures-CRUD/LecturesForm";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";
import { notFound } from "next/navigation";
import React from "react";

export default async function UpdateLecturePage(props: {
  params: Promise<{ lectureId: string; courseId: string }>;
}) {
  const { lectureId, courseId } = await props.params;

  const [course, lecture] = await prisma.$transaction([
    prisma.course.findUnique({ where: { id: courseId } }),
    prisma.lecture.findUnique({ where: { id: lectureId } }),
  ]);

  if (!course || !lecture) return notFound();

  return (
    <PageLayout title="تعديل المحور" description={`تابع للدورة: "${course.title}"`}>
      <LecturesForm
        defaultValues={lecture}
        courseId={courseId}
        mode="update"
        onAction={async ({ data: request }) => {
          "use server";

          // الفحص والتحقق من صحة القيم
          const { success, data, error } = lectureSchema.safeParse(request);

          if (!success) {
            return { success: false, errors: error.formErrors.fieldErrors };
          }

          const [video, audio, pdf, posterUrl] = await Promise.all([
            handleFileUpdate(lecture.video, data.video, "courses/video/"),
            handleFileUpdate(lecture.audio, data.audio, "courses/audio/"),
            handleFileUpdate(lecture.pdf, data.pdf, "courses/pdf/"),
            handleFileUpdate(lecture.posterUrl, data.posterUrl, "courses/image/"),
          ]);

          await prisma.lecture.update({
            where: { id: lectureId, courseId },
            data: { ...data, posterUrl, video, audio, pdf },
          });

          revalidatePath(`/courses/${courseId}`);
          revalidatePath(`/courses/${courseId}/${lectureId}`);
          revalidatePath(`/admin/courses/lectures/${courseId}`);

          return { success: true, message: "تم تعديل المحور بنجاح" };
        }}
      />
    </PageLayout>
  );
}
