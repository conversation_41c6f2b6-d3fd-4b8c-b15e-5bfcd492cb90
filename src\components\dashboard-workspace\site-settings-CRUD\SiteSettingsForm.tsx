"use client"

import React, { useEffect, useMemo, useState } from "react"
import { SiteSettings } from "@prisma/client"
import { Edit } from "lucide-react"
import { toast } from "sonner"

import DataForm from "@/components/dashboard/components/form/DataForm"
import { <PERSON><PERSON> } from "@/components/dashboard/components/ui/Button"
import FieldAttachments from "@/components/dashboard/components/ui/FieldAttachments"
import { Input } from "@/components/dashboard/components/ui/Input"
import { Textarea } from "@/components/dashboard/components/ui/Textarea"
import { updateMyStoryVideoUrl } from "@/app/(dashboard)/admin/site-settings/actions"

type FieldsType = {
  label: string
  name: keyof SiteSettings
  type?: React.HTMLInputTypeAttribute | "textarea"
  defaultValue: string
  description?: string
  placeholder?: string
}[]

type ServerActionResult = {
  success: boolean
  message: string
  errors?: Record<string, string[]>
}

export default function SiteSettingsForm(props: {
  siteSettings: SiteSettings
  onSubmit?: (
    updatedSettings: Partial<SiteSettings>
  ) => Promise<ServerActionResult>
}) {
  const { siteSettings, onSubmit } = props
  const [isEditMode, setIsEditMode] = useState(false)
  const [formValues, setFormValues] = useState<{ [key: string]: string }>({})
  const [isFormModified, setIsFormModified] = useState(false)
  const [isSubmitting, setIsSubmitting] = useState(false)

  // Define fields using useMemo to avoid recreating on each render
  const fields: FieldsType = useMemo(
    () => [
      {
        name: "phone",
        label: "رقم الهاتف",
        defaultValue: siteSettings?.phone ?? "",
        placeholder: "+966 XXX XXX XXX",
        type: "tel",
      },
      {
        name: "email",
        label: "البريد الإلكتروني",
        type: "email",
        defaultValue: siteSettings?.email ?? "",
        placeholder: "<EMAIL>",
      },
      {
        name: "facebookUrl",
        label: "رابط الفيسبوك",
        defaultValue: siteSettings?.facebookUrl ?? "",
        placeholder: "https://www.facebook.com/...",
        type: "url",
      },
      {
        name: "twitterUrl",
        label: "رابط تويتر",
        defaultValue: siteSettings?.twitterUrl ?? "",
        placeholder: "https://www.twitter.com/...",
        type: "url",
      },
      {
        name: "instagramUrl",
        label: "رابط الانستجرام",
        defaultValue: siteSettings?.instagramUrl ?? "",
        placeholder: "https://www.instagram.com/...",
        type: "url",
      },
      {
        name: "youtubeUrl",
        label: "رابط قناة اليوتيوب",
        defaultValue: siteSettings?.youtubeUrl ?? "",
        placeholder: "https://www.youtube.com/...",
        type: "url",
      },
      {
        name: "tiktokUrl",
        label: "رابط التيك توك",
        defaultValue: siteSettings?.tiktokUrl ?? "",
        placeholder: "https://www.tiktok.com/...",
        type: "url",
      },
      {
        name: "subscriptionInstructions",
        label: "تعليمات الاشتراك",
        defaultValue: siteSettings?.subscriptionInstructions ?? "",
        placeholder: "اكتب تعليمات الاشتراك ...",
        description:
          "تعليمات الاشتراك في الدورات تدريبية المدفوعة تظهر في النافذة المنبثقة عندما يقوم المستخدم بالنقر على زر اشتراك في دورة مدفوع",
        type: "textarea",
      },
    ],
    [siteSettings]
  )

  // Initialize form values from siteSettings
  useEffect(() => {
    const initialValues: { [key: string]: string } = {}
    fields.forEach((field) => {
      initialValues[field.name] = field.defaultValue
    })
    setFormValues(initialValues)
  }, [siteSettings, fields])

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target

    // Update form values with the new value
    const updatedValues = { ...formValues, [name]: value }
    setFormValues(updatedValues)

    // Check if any field has been modified compared to original siteSettings
    const anyFieldModified = fields.some((field) => {
      const fieldName = field.name
      const originalValue = siteSettings[fieldName] || ""
      const currentValue = updatedValues[fieldName] || ""
      return originalValue !== currentValue
    })

    setIsFormModified(anyFieldModified)
  }

  // State for form errors
  const [formErrors, setFormErrors] = useState<Record<string, string[]>>({})

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setFormErrors({})
    setIsSubmitting(true)

    try {
      // Convert form values to a Partial<SiteSettings> object
      const updatedSettings: Partial<SiteSettings> = {}

      // Only include fields that have changed
      fields.forEach((field) => {
        const fieldName = field.name
        const originalValue = siteSettings[fieldName] || ""
        const currentValue = formValues[fieldName] || ""

        if (originalValue !== currentValue) {
          // Type assertion to handle the string assignment to SiteSettings properties
          updatedSettings[fieldName] = currentValue as any
        }
      })

      // If onSubmit prop is provided, call it with the updated settings
      if (onSubmit) {
        const result = await onSubmit(updatedSettings)

        if (result.success) {
          toast.success(result.message || "تم حفظ التعديلات بنجاح")
          setIsEditMode(false)
          setIsFormModified(false)
        } else {
          toast.error(result.message || "حدث خطأ أثناء حفظ التعديلات")

          // Set form errors if any
          if (result.errors) {
            setFormErrors(result.errors)
          }
        }
      } else {
        // Fallback if no onSubmit is provided
        toast.success("تم حفظ التعديلات بنجاح")
        setIsEditMode(false)
        setIsFormModified(false)
      }
    } catch (error) {
      toast.error("حدث خطأ أثناء حفظ التعديلات")
      console.error(error)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Reset form to original values
  const handleCancel = () => {
    const initialValues: { [key: string]: string } = {}
    fields.forEach((field) => {
      initialValues[field.name] = field.defaultValue
    })
    setFormValues(initialValues)
    setIsEditMode(false)
    setIsFormModified(false)
    setFormErrors({})
  }

  return (
    <div>
      <form className="space-y-10" onSubmit={handleSubmit}>
        {fields.map((field) => {
          const fieldErrors = formErrors[field.name]
          return (
            <FieldAttachments
              htmlFor={field.name}
              label={field.label}
              key={field.name}
              description={field.description}
              errorMessage={fieldErrors}
            >
              {field.type === "textarea" ? (
                <Textarea
                  name={field.name}
                  placeholder={field.placeholder}
                  value={formValues[field.name] || ""}
                  onChange={handleInputChange}
                  disabled={!isEditMode}
                  hasError={!!fieldErrors}
                  className="min-h-40"
                />
              ) : (
                <Input
                  name={field.name}
                  placeholder={field.placeholder}
                  value={formValues[field.name] || ""}
                  onChange={handleInputChange}
                  type={field.type}
                  disabled={!isEditMode}
                  hasError={!!fieldErrors}
                />
              )}
            </FieldAttachments>
          )
        })}

        <div className="flex flex-col gap-3 md:flex-row-reverse">
          {!isEditMode ? (
            <Button type="button" onClick={() => setIsEditMode(true)}>
              تمكين التعديل <Edit />
            </Button>
          ) : (
            <>
              <Button
                type="submit"
                disabled={!isFormModified || isSubmitting}
                isLoading={isSubmitting}
                loadingText="جاري الحفظ..."
              >
                حفظ التعديلات
              </Button>
              <Button
                type="button"
                variant="light"
                onClick={handleCancel}
                disabled={isSubmitting}
              >
                إلغاء
              </Button>
            </>
          )}
        </div>
      </form>
      <div className="mt-32">
        <hr className="mb-10 border-dashed" />
        {/* <h2 className="text-3xl mb-10 font-medium">قصتي مع العلاج الشعوري</h2> */}
        <DataForm<{ myStoryVideoUrl: string; myStoryVideoThumbnailUrl: string }>
          fields={[
            {
              accessorKey: "myStoryVideoUrl",
              label: "فيديو القصة",
              fieldType: "fileUploader",
              fileUploaderConfig: {
                acceptedFileTypes: ["video/*"],
                dataType: "string",
              },
              description:
                "فيديو القصة يظهر في الصفحة الرئيسية في قسم قصتي مع العلاج الشعوري",
            },
            {
              accessorKey: "myStoryVideoThumbnailUrl",
              label: "صورة مصغرة للفيديو",
              fieldType: "fileUploader",
              fileUploaderConfig: {
                acceptedFileTypes: ["image/jpeg", "image/png", "image/webp"],
                dataType: "string",
              },
            },
          ]}
          mode="update"
          defaultValues={{
            myStoryVideoUrl: siteSettings?.myStoryVideoUrl,
            myStoryVideoThumbnailUrl: siteSettings?.myStoryVideoThumbnailUrl
          }}
          actionButtonLabel="حفظ"
          onAction={updateMyStoryVideoUrl}
        />
      </div>
    </div>
  )
}
