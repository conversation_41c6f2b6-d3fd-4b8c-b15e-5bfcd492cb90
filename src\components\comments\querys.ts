import { dataLimits } from "@/configs/site"
import { EntityType } from "@prisma/client"

import prisma from "@/lib/prisma"

export const getComments = async ({
  page,
  entity,
  entityId,
}: {
  page: number
  entityId: string
  entity: EntityType
}) => {
  const limit = dataLimits.comments
  const skip = (page - 1) * limit

  const [data, totalCount] = await prisma.$transaction([
    prisma.comment.findMany({
      orderBy: { createdAt: "desc" },
      where: { entity, entityId },
      take: limit,
      skip,
    }),
    prisma.comment.count({
      where: { entity, entityId },
    }),
  ])

  const totalPages = Math.ceil(totalCount / limit)

  return {
    data,
    pagination: {
      currentPage: page,
      totalPages,
    },
  }
}
