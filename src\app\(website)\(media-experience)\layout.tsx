import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers"
import Tabs from "@/components/ui/tabs"
import TitleSection from "@/components/ui/title-section"

// ==================================================================================
// تخطيط يشمل كل صفحات التجربة الإعلامية
// ==================================================================================
export default function LayoutMediaExperience({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="pt-16">
      <HeaderPages />

      <div className="mt-16 mb-24 space-y-6 px-3 md:px-10">
        <Tabs />
        <div className="overflow-clip rounded-lg border p-4 md:p-6">
          <div className="w-full scroll-mt-24 space-y-7" id="pagination">
            {children}
          </div>
        </div>
      </div>
    </div>
  )
}

// ==================================================================================
// رأس الصفحة الذي يعرض عنوان تجربتي الإعلامية
// ==================================================================================
function HeaderPages() {
  return (
    <div className="bg-secondary relative py-32">
      <DividerRedTop />
      <TitleSection
        title={<h1>تجربتي الإعلامية</h1>}
        description="مجموعة دورات شاملة لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات، من خلال تقنيات فعّالة لتحقيق التوازن النفسي والجسدي."
        classTitle="text-background"
        classDescription="text-background"
      />
      <DividerRedBottom />
    </div>
  )
}
