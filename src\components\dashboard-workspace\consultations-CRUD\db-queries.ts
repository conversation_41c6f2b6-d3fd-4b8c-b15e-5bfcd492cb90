"use server"

import { revalidateTag, unstable_cache } from "next/cache"
import { Consultation, Prisma } from "@prisma/client"

import { revalidateConsultations } from "@/lib/cache-invalidation"
import prisma from "@/lib/prisma"

type GetConsultationsResponse = Promise<{
  data: Consultation[]
  nextCursor: string | null
}>
type GetConsultationsProps = {
  cursorId?: string | null
  limit?: number
  where?: Prisma.ConsultationWhereInput | undefined
}

export const getConsultations = async ({
  cursorId,
  limit = 30,
  where,
}: GetConsultationsProps): GetConsultationsResponse => {
  // Fetch consultations from the database with optional cursor and limit
  const consultations = await prisma.consultation.findMany({
    where,
    take: limit + 1,
    orderBy: { createdAt: "desc" },
    ...(cursorId && { cursor: { id: cursorId }, skip: 1 }),
  })

  const hasMore = consultations.length > limit
  const nextCursor = hasMore ? consultations[limit].id : null

  return { data: consultations.slice(0, limit), nextCursor }
}

export const getConsultationCache = unstable_cache(
  async () => await getConsultations({ limit: 50 }),
  [],
  {
    tags: ["consultations", "consultations-all"],
  }
)

export const getConsultationUnReadCache = unstable_cache(
  async () => await getConsultations({ limit: 100, where: { read: false } }),
  [],
  {
    tags: ["consultations", "consultations-unread"],
  }
)

export const getConsultationUnReadCountCache = unstable_cache(
  async () => await prisma.consultation.count({ where: { read: false } }),
  [],
  {
    tags: ["consultations", "consultations-unread-count"],
  }
)

export async function markConsultationAsRead(id: string) {
  await prisma.consultation.update({
    where: { id },
    data: { read: true },
  })

  revalidateTag("consultations")
  await revalidateConsultations()
  return { success: true }
}

export async function deleteConsultation(id: string) {
  await prisma.consultation.delete({
    where: { id },
  })
  revalidateTag("consultations")
  await revalidateConsultations()
  return { success: true, message: "تم حذف الاستشارة بنجاح" }
}
