import { Suspense } from "react"
import Image from "next/image"
import { redirect } from "next/navigation"
import { formatDate } from "@/utils/formaters"
import { Prisma } from "@prisma/client"
import { BotMessageSquare, ChevronLeft } from "lucide-react"

import { verifySession } from "@/lib/auth"
import BadgePrice from "@/components/ui/badge-price"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"

import { UnsubscribeDialog } from "../_components/unsubscribe-dialog"
import { getUserCourseOrders } from "../_lib/querys"

//
// --------------------------------------------------------------------------------------
// # صفحة تعرض الكورسات التي اشترك بها المستخدم
// --------------------------------------------------------------------------------------
//

export default function UserSubscribedCoursesPage() {
  return (
    <div>
      <h1 className="text-2xl font-semibold">اشتراكات الدورات تدريبية</h1>
      <p className="text-muted mt-2 text-sm">
        هذه الصفحة تعرض كل الدورات تدريبية الخاصة بك التي قمت بالاشتراك فيها
        بطريقة مدفوعة او مجانية.
      </p>
      <hr className="border-muted/50 my-6 border-dashed" />
      <Suspense fallback={<SubscribedCoursesSkeleton />}>
        <SubscribedCourses />
      </Suspense>
    </div>
  )
}

async function SubscribedCourses() {
  const session = await verifySession()
  if (!session) redirect("/auth/login")

  const userCourseOrders = await getUserCourseOrders(session?.id)

  if (!userCourseOrders.length) {
    return (
      <div className="border-muted/50 bg-muted/5 flex w-full flex-col items-center justify-center gap-2 rounded-xl border border-dashed px-3 py-6 text-center text-balance text-zinc-400">
        <BotMessageSquare className="size-28" />
        <span className="text-xl font-semibold">
          ليس لديك اشتراكات حتا الآن
        </span>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 place-items-center gap-y-6 sm:grid-cols-2 sm:gap-3 md:grid-cols-1 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
      {userCourseOrders.map((order) => (
        <SubscribedCourseCard key={order.id} courseOrder={order} />
      ))}
    </div>
  )
}

async function SubscribedCourseCard({
  courseOrder,
}: {
  courseOrder: Prisma.CourseOrderGetPayload<{ include: { course: true } }>
}) {
  const course = courseOrder.course

  return (
    <div className="border-muted/10 flex w-full max-w-80 flex-col items-center gap-6 rounded-xl border bg-white p-4 shadow-lg">
      <div className="bg-muted relative aspect-square w-full overflow-clip rounded-lg border shadow-xs">
        <Image
          fill
          alt={course.title}
          src={course.posterUrl}
          sizes="(max-width: 640px) 80vw, 40vw"
          className="bg-muted h-auto object-cover"
        />
      </div>
      <p className="text-muted w-full text-center text-xl font-semibold text-balance">
        {course.title}
      </p>
      <div className="border-muted/10 *:bg-muted/5 *:text-muted grid w-full grid-cols-5 gap-x-1 gap-y-1 rounded-lg border bg-white p-3 shadow-xs *:flex *:items-center *:gap-1 *:rounded-sm *:px-3 *:py-1 *:text-xs *:text-nowrap [&_span]:text-xs [&_span]:font-medium [&_svg]:mr-auto [&_svg]:size-4 [&_svg]:shrink-0">
        <p className="col-span-3">
          سعر الاشتراك <ChevronLeft />
        </p>
        <p className="col-span-2">
          <BadgePrice price={courseOrder.price} />
        </p>
        <p className="col-span-3">
          تاريخ الاشتراك <ChevronLeft />
        </p>
        <p className="col-span-2">{formatDate(courseOrder.createdAt)}</p>
        <p className="col-span-3">
          عدد المحاور <ChevronLeft />
        </p>
        <p className="col-span-2">{course.modulesCount}</p>
      </div>
      <div className="flex w-full flex-col gap-3">
        <UnsubscribeDialog entity="courseOrders" orderId={courseOrder.id}>
          <Button variant="outline">إلغاء الاشتراك</Button>
        </UnsubscribeDialog>
      </div>
    </div>
  )
}

function SubscribedCoursesSkeleton() {
  const array = Array.from({ length: 3 })

  return (
    <div className="grid grid-cols-1 place-content-center place-items-center gap-y-6 sm:grid-cols-2 sm:gap-3 md:grid-cols-1 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
      {array.map((_, i) => (
        <Skeleton key={i} className="h-[550px] w-full max-w-72 rounded-xl" />
      ))}
    </div>
  )
}
