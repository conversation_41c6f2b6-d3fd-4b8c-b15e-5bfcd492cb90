import { revalidatePath, revalidateTag } from "next/cache"

import { revalidateBooks } from "@/lib/cache-invalidation"
import prisma from "@/lib/prisma"
import BooksTable from "@/components/dashboard-workspace/books-CRUD/BooksTable"
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles"

export default async function page() {
  const books = await prisma.book.findMany({ orderBy: { createdAt: "desc" } })

  return (
    <BooksTable
      data={books}
      onDelete={async ({ data }) => {
        "use server"

        await prisma.book.deleteMany({
          where: { id: { in: data.map((book) => book.id) } },
        })

        await Promise.all(
          data.flatMap((book) => [
            deleteFileInR2({ fileUrl: book.coverImage }),
            deleteFileInR2({ fileUrl: book.bookUrl }),
          ])
        )

        revalidateTag("pageTitlesAndIds")
        revalidatePath("/admin/books")
        revalidatePath("/(website)/media-experience/books", "layout")
        revalidateTag("booksPagesCount")
        await revalidateBooks()

        return { success: true, message: "تم الحذف بنجاح" }
      }}
    />
  )
}
