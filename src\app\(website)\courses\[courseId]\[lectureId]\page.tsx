import { Suspense } from "react"
import { Metada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { siteName } from "@/configs/site"
import { Lecture } from "@prisma/client"
import { Download } from "lucide-react"

import { getCourseLectures } from "@/lib/querys"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import PageDivider from "@/components/ui/page-divider"
import { PdfIcon } from "@/components/ui/svg/icons"
import Comments from "@/components/comments/comments"

type LecturePageProps = {
  params: Promise<{ courseId: string; lectureId: string }>
}

export const dynamic = "force-static"

export async function generateMetadata({
  params,
}: LecturePageProps): Promise<Metadata> {
  const { courseId, lectureId } = await params
  const lectures = await getCourseLectures({ courseId })
  const find = lectures?.find((lecture) => lecture.id === lectureId)
  if (!find) return notFound()

  return {
    title: find.title,
    description: find.seoDescription,
    keywords: find.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar_AR",
      title: find.title,
      siteName: siteName,
      images: find.posterUrl,
      description: find.seoDescription,
      url: `/courses/${courseId}/${find.id}`,
    },
  }
}

export default async function LecturePage({ params }: LecturePageProps) {
  const { courseId, lectureId } = await params
  const lectures = await getCourseLectures({ courseId })
  const find = lectures?.find((lecture) => lecture.id === lectureId)
  if (!find) return notFound()

  return (
    <div className="min-h-svh px-4 py-20 sm:py-28 lg:px-6">
      <h1 className="text-xl font-semibold">{find.title}</h1>
      <PageDivider
        pageContents={<LectureContent {...find} />}
        titleMenu="قائمة محاور الدورة"
        menuItems={lectures?.map((lecture) => (
          <LectureListCard
            key={lecture.id}
            lecture={lecture}
            courseId={courseId}
            currentLectureId={find.id}
          />
        ))}
        comments={
          <Suspense fallback={<div>جاري التحميل...</div>}>
            <Comments
              entity="lecture"
              entityId={find.id}
              pathRevalidate={`/courses/${courseId}/${lectureId}`}
            />
          </Suspense>
        }
      />
    </div>
  )
}

function LectureListCard({
  currentLectureId,
  courseId,
  lecture,
}: {
  lecture: Lecture
  courseId: string
  currentLectureId: string
}) {
  const isCurrent = lecture.id === currentLectureId
  return (
    <Link
      href={`/courses/${courseId}/${lecture.id}`}
      className={cn(
        "flex gap-3 rounded-lg bg-linear-to-r py-3",
        isCurrent ? "from-primary/60 border-l" : "group hover:from-muted/30"
      )}
    >
      <div
        className={cn(
          "bg-muted/80 relative aspect-video w-1/3 shrink-0 overflow-clip rounded-md",
          isCurrent && "border-primary/50 border"
        )}
      >
        <Image
          sizes="(max-width: 768px) 30vw, 10vw"
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          src={lecture.posterUrl}
          alt={lecture.title}
          fill
        />
      </div>
      <p className="line-clamp-2 text-xs leading-relaxed lg:text-sm lg:leading-relaxed">
        {lecture.title}
      </p>
    </Link>
  )
}

function LectureContent(props: Lecture) {
  return (
    <div className="space-y-16">
      <LectureVideo video={props.video} poster={props.posterUrl} />
      <LectureAudio audio={props.audio} />
      <LecturePdf pdf={props.pdf} title={props.title} />
    </div>
  )
}

function LectureVideo({
  video,
  poster,
}: {
  video: Lecture["video"]
  poster: string
}) {
  if (!video) return null

  return (
    <video
      controls
      preload="none"
      poster={poster}
      className="aspect-video w-full rounded-lg shadow-md shadow-black/30"
    >
      <source src={video} />
    </video>
  )
}

function LectureAudio({ audio }: { audio: Lecture["audio"] }) {
  if (!audio) return null
  return (
    <div className="space-y-6">
      <div className="flex w-full flex-nowrap items-center gap-4">
        <h2 id="lectures" className="text-primary scroll-mt-20">
          مقطع صوتي
        </h2>
        <hr className="border-primary mt-1.5 flex-1" />
      </div>
      <figure className="bg-secondary rounded-lg py-2 shadow-md shadow-black/30">
        <audio
          controls
          preload="none"
          src={audio}
          className="w-full mix-blend-exclusion brightness-105 invert"
        />
      </figure>
    </div>
  )
}

function LecturePdf({ pdf, title }: { pdf: Lecture["pdf"]; title: string }) {
  if (!pdf) return null

  return (
    <div className="space-y-6">
      <div className="flex w-full flex-nowrap items-center gap-4">
        <h2 id="lectures" className="text-primary scroll-mt-20">
          ملف pdf
        </h2>
        <hr className="border-primary mt-1.5 flex-1" />
      </div>

      {/* الملف */}
      <div className="bg-secondary text-background flex w-full items-center gap-3 rounded-lg p-4 shadow-md shadow-black/30">
        <PdfIcon className="size-14 shrink-0" />
        <div>
          <span className="line-clamp-2 text-xs leading-relaxed">{title}</span>
        </div>
        <Button asChild variant="outlineDark" className="mr-auto">
          <a href={pdf} target="_blank" download={title}>
            <span className="sr-only">تنزيل الملف</span>
            <Download />
          </a>
        </Button>
      </div>
    </div>
  )
}
