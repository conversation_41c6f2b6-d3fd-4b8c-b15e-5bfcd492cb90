import prisma from "@/lib/prisma";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    // الحصول على الدورات تدريبية المدفوعة فقط (السعر أكبر من صفر)
    const courses = await prisma.course.findMany({
      where: {
        price: {
          gt: 0
        }
      },
      select: {
        id: true,
        title: true,
        price: true
      },
      orderBy: {
        title: 'asc'
      }
    });

    return NextResponse.json({
      success: true,
      courses
    });
  } catch (error) {
    console.error("Error fetching paid courses:", error);
    return NextResponse.json(
      {
        success: false,
        message: "حدث خطأ أثناء جلب الدورات تدريبية المدفوعة"
      },
      { status: 500 }
    );
  }
}
