import { MetadataRoute } from "next"
import { Videos } from "next/dist/lib/metadata/types/metadata-types"

import prisma from "@/lib/prisma"

const getUrl = (segment: `/${string}`) => {
  return `${process.env.DOMAIN}${segment}`
}

// كل 24 ساعة
export const revalidate = 86400

//  القيم المقترحة:
//  - `1`: للصفحة الرئيسية
//  - `0.8`: للصفحات الرئيسية (المنتجات، المدونة، الخدمات)
//  - `0.6`: لصفحات المنتجات الفردية أو مقالات المدونة
//  - `0.4`: للصفحات الثانوية
//  - `0.2`: للصفحات الأقل أهمية
export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  return [
    {
      url: process.env.DOMAIN ?? "",
      lastModified: new Date().toISOString(),
      videos: await getVideoMyStory(),
      changeFrequency: "monthly",
      priority: 1,
    },
    {
      url: getUrl("/courses"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/consultation"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.6,
    },
    {
      url: getUrl("/about-me"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.7,
    },
    {
      url: getUrl("/auth/login"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.4,
    },
    {
      url: getUrl("/auth/signup"),
      lastModified: new Date().toISOString(),
      changeFrequency: "yearly",
      priority: 0.4,
    },
    {
      url: getUrl("/blog"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/articles"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/interviews"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/books"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    {
      url: getUrl("/faq"),
      lastModified: new Date().toISOString(),
      changeFrequency: "monthly",
      priority: 0.8,
    },
    ...(await getBlogPostsSitemap()),
    ...(await getArticlesSitemap()),
    ...(await getInterviewsSitemap()),
    ...(await getBooksSitemap()),
    ...(await getCoursesSitemap()),
  ]
}

// دالة ترجع عناصر sitemap لمنشورات المدونة
export async function getBlogPostsSitemap(): Promise<MetadataRoute.Sitemap> {
  const blogPostIds = await prisma.blogPost.findMany({
    select: { id: true, image: true },
  })

  return blogPostIds.map((post) => ({
    url: getUrl(`/blog/${post.id}`),
    lastModified: new Date().toISOString(),
    changeFrequency: "monthly",
    images: [post.image],
    priority: 0.6,
  }))
}

// دالة ترجع عناصر sitemap للمقالات
export async function getArticlesSitemap(): Promise<MetadataRoute.Sitemap> {
  const articleIds = await prisma.article.findMany({
    select: { id: true },
  })

  return articleIds.map((article) => ({
    url: getUrl(`/articles/${article.id}`),
    lastModified: new Date().toISOString(),
    changeFrequency: "monthly",
    priority: 0.6,
  }))
}

// دالة ترجع عناصر sitemap للمقابلات
export async function getInterviewsSitemap(): Promise<MetadataRoute.Sitemap> {
  const interviewIds = await prisma.interview.findMany({
    select: { id: true },
  })

  return interviewIds.map((interview) => ({
    url: getUrl(`/interviews/${interview.id}`),
    lastModified: new Date().toISOString(),
    changeFrequency: "monthly",
    priority: 0.6,
  }))
}

// دالة ترجع عناصر sitemap للكتب
export async function getBooksSitemap(): Promise<MetadataRoute.Sitemap> {
  const bookIds = await prisma.book.findMany({
    select: { id: true, coverImage: true },
  })

  return bookIds.map((book) => ({
    url: getUrl(`/books/${book.id}`),
    lastModified: new Date().toISOString(),
    changeFrequency: "monthly",
    images: [book.coverImage],
    priority: 0.6,
  }))
}

// دالة ترجع عناصر sitemap للدورات تدريبية
export async function getCoursesSitemap(): Promise<MetadataRoute.Sitemap> {
  const courseIds = await prisma.course.findMany({
    select: { id: true, posterUrl: true },
  })

  return courseIds.map((course) => ({
    url: getUrl(`/courses/${course.id}`),
    lastModified: new Date().toISOString(),
    changeFrequency: "monthly",
    images: [course.posterUrl],
    priority: 0.7,
  }))
}

async function getVideoMyStory(): Promise<Videos[] | undefined> {
  const video = await prisma.siteSettings.findUnique({
    where: { id: 1 },
    select: { myStoryVideoUrl: true, myStoryVideoThumbnailUrl: true },
  })

  if (!video) return undefined

  return [
    {
      title: "قصتي مع العلاج الشعوري",
      description: "تعرف على قصتي مع العلاج الشعوري وكيف تمكنت من تحقيق النجاح",
      thumbnail_loc: video.myStoryVideoThumbnailUrl,
    },
  ]
}
