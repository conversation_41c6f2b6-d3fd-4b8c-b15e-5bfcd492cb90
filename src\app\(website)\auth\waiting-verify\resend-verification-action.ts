"use server"

import { sendEmail } from "@/lib/email"
import prisma from "@/lib/prisma"

type ResendVerificationResult = {
  success: boolean
  message: string
}

export async function resendVerificationAction(
  email: string,
  userName: string
): Promise<ResendVerificationResult> {
  if (!email) {
    return {
      success: false,
      message: "البريد الإلكتروني غير صالح أو مفقود.",
    }
  }

  // التحقق من انتهاء فترة صلاحية verificationTokenExpiry
  const user = await prisma.user.findUnique({
    where: { email, emailVerified: false },
    select: { verificationTokenExpiry: true },
  })

  // التحقق من انتهاء فترة صلاحية verificationTokenExpiry
  if (
    user &&
    user.verificationTokenExpiry &&
    user.verificationTokenExpiry > new Date()
  ) {
    return {
      success: false,
      message:
        "لا يمكنك إرسال رابط التحقق جديد في الوقت الحالي. يرجى المحاولة مرة أخرى في وقت لاحق.",
    }
  }

  try {
    // إرسال بريد إلكتروني جديد للتحقق
    const { isSended, verificationToken, verificationTokenExpiry } =
      await sendEmail({
        userEmail: email,
        userName: userName,
      })

    if (!isSended) {
      return {
        success: false,
        message: "حدث خطأ أثناء إرسال رابط التحقق. يرجى المحاولة مرة أخرى.",
      }
    }

    // تحديث رمز التحقق للمستخدم
    await prisma.user.update({
      where: { email, emailVerified: false },
      data: {
        verificationToken,
        verificationTokenExpiry,
      },
    })

    return {
      success: true,
      message: "تم إرسال رابط تحقق جديد إلى بريدك الإلكتروني.",
    }
  } catch (error) {
    console.error("Error resending verification email:", error)
    return {
      success: false,
      message: "حدث خطأ أثناء إرسال رابط التحقق. يرجى المحاولة مرة أخرى.",
    }
  }
}
