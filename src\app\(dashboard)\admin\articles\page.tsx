import { revalidatePath, revalidateTag } from "next/cache"

import { revalidateArticles } from "@/lib/cache-invalidation"
import prisma from "@/lib/prisma"
import ArticlesTable from "@/components/dashboard-workspace/articles-CRUD/ArticlesTable"

export default async function ArticlesPage() {
  const articles = await prisma.article.findMany({
    orderBy: { createdAt: "desc" },
  })

  return (
    <ArticlesTable
      data={articles}
      onDelete={async ({ data }) => {
        "use server"

        await prisma.article.deleteMany({
          where: { id: { in: data.map((article) => article.id) } },
        })

        revalidateTag("pageTitlesAndIds")
        revalidatePath("/admin/articles")
        revalidatePath("/(website)/media-experience/articles", "layout")
        revalidateTag("articlesPagesCount")
        await revalidateArticles()

        return { success: true, message: "تم الحذف بنجاح" }
      }}
    />
  )
}
