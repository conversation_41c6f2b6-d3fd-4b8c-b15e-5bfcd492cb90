// import "@/app/globals.css"

import type { Metadata } from "next"
import { Noto_Kufi_Arabic } from "next/font/google"
import { siteName } from "@/configs/site"

import Footer from "@/components/ui/footer/footer"
import Nav from "@/components/ui/nav/nav"
import { Toaster } from "@/components/ui/sonner"
import { SessionProvider } from "@/components/session-provider"

const fontNotoKufi = Noto_Kufi_Arabic({
  weight: "variable",
  subsets: ["arabic"],
  display: "swap",
})

export const metadata: Metadata = {
  title: siteName,
  generator: "Next.js",
  creator: "ناهد باشطح",
  applicationName: siteName,
  authors: [{ name: "ناهد باشطح" }],
  publisher: "اكاديمة الدكتورة ناهد باشطح",
  metadataBase: new URL(String(process.env.DOMAIN)),
  description:
    "اكاديمية د. ناهد باشطح لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات. تقنيات فعّالة لتحقيق التوازن النفسي والجسدي",
  openGraph: {
    title: siteName,
    type: "website",
    locale: "ar",
    siteName: siteName,
    description:
      "اكاديمية د. ناهد باشطح لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات. تقنيات فعّالة لتحقيق التوازن النفسي والجسدي",
  },
  twitter: {
    card: "summary_large_image",
    title: siteName,
    description:
      "اكاديمية د. ناهد باشطح لمساعدتك في التحرر من المشاعر المكبوتة، تحسين قراراتك، التغلب على القلق، والتعافي من الصدمات. تقنيات فعّالة لتحقيق التوازن النفسي والجسدي",
    site: siteName,
    creator: "ناهد باشطح",
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      "max-snippet": -1,
      "max-image-preview": "large",
      "max-video-preview": -1,
    },
  },
}

export default function Layout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <main
      className={`${fontNotoKufi.className} mx-auto max-w-(--breakpoint-2xl)`}
    >
      <SessionProvider>
        <Nav />
        {children}
        <Footer />
        <Toaster richColors position="top-center" />
      </SessionProvider>
    </main>
  )
}
