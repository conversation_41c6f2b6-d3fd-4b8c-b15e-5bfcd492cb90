import { formatDate } from "@/utils/formaters"
import { Comment } from "@prisma/client"
import { User } from "lucide-react"

import { cn } from "@/lib/utils"

import { DeleteComment } from "./delete-comment"

// ==================================================================================
// مكون التعليق يستخدم وكأنه بطاقة
// ==================================================================================
export function CommentCard({
  comment,
  usingAnimate,
  pathRevalidate,
}: {
  comment: Comment
  usingAnimate?: boolean
  pathRevalidate: string
}) {
  return (
    <div
      className={cn(
        "my-4 flex flex-nowrap items-start gap-5 py-4 first:transition-all",
        usingAnimate &&
          "first:animate-in first:fade-in-0 first:slide-in-from-top first:delay-75 first:duration-500"
      )}
    >
      <div className="bg-secondary flex size-12 shrink-0 items-center justify-center overflow-clip rounded-full">
        <User className="text-background size-7" />
      </div>
      <div className="relative flex-1 space-y-2">
        <div className="flex flex-nowrap items-center gap-2">
          <p className="text-sm font-semibold">{comment.userName}</p>
          <p className="text-xs">{formatDate(comment.createdAt)}</p>
          <div className="absolute top-0 left-0">
            <DeleteComment comment={comment} pathRevalidate={pathRevalidate} />
          </div>
        </div>
        <p className="text-sm leading-relaxed text-wrap">{comment.comment}</p>
      </div>
    </div>
  )
}
