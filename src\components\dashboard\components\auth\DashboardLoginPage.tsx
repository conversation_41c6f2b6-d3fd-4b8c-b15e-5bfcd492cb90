import { z } from "zod"

import prisma from "@/lib/prisma"

import { formEntries } from "../../lib/utils"
import { comparePasswords, createAdminSession } from "./admin-session"
import AdminLoginForm, { LoginActionType } from "./DashboardLoginForm"

export default async function AdminLoginPage() {
  const handleAction: LoginActionType = async (formData) => {
    "use server"
    // استخراج البيانات من الفورم
    const request = formEntries(formData) as {
      password: string
      email: string
    }

    const schema = z.object({
      email: z
        .string()
        .trim()
        .email("البريد الإلكتروني غير صالح")
        .min(12, "لا يمكن ان يكون البريد الإلكتروني اقل من 12 حرف"),
      password: z
        .string()
        .trim()
        .max(40, "الحد الأقصى لكلمة المرور هو 40 حرف")
        .min(6, "لا يمكن ان تكون كلمة المرور اقل من 6 احرف"),
    })

    const { success, error, data } = schema.safeParse(request)
    if (!success) {
      return { success: false, errors: error.formErrors.fieldErrors }
    }

    const admin = await prisma.admin.findUnique({
      where: { email: data.email },
    })

    if (!admin) {
      return {
        success: false,
        message: "كلمة المرور او البريد الإلكتروني غير صحيح",
      }
    }

    const password = await comparePasswords(data.password, admin?.password)

    if (!password) {
      return {
        success: false,
        message: "كلمة المرور او البريد الإلكتروني غير صحيح",
      }
    }
    if (admin.status === "disabled") {
      return { success: false, message: "تم إيقاف حسابك من قٍبل المدير العام" }
    }

    await createAdminSession({
      id: admin.id,
      name: admin.name,
      email: admin.email,
      status: admin.status,
      accessiblePages: admin.accessiblePages,
      avatarUrl: admin.avatarUrl,
      role: admin.role,
      pushSubscription: admin.pushSubscription,
    })

    return { success: true }
  }

  return (
    <>
      <div className="flex min-h-screen flex-1 flex-col justify-center bg-white px-4 font-serif antialiased lg:px-6 dark:bg-gray-950">
        <div className="sm:mx-auto sm:w-full sm:max-w-sm">
          <h3 className="text-center text-lg font-semibold text-gray-900 dark:text-gray-50">
            مرحبًا بعودتك
          </h3>
          <p className="text-center text-sm text-gray-500 dark:text-gray-500">
            أدخل بيانات الاعتماد الخاصة بك للوصول إلى حسابك.
          </p>
          <AdminLoginForm loginAction={handleAction} />
          <p className="mt-6 text-sm text-gray-500 dark:text-gray-500">
            نسيت كلمة المرور؟{" "}
            <a
              href="#"
              className="font-medium text-blue-500 hover:text-blue-600 dark:text-blue-500 dark:hover:text-blue-600"
            >
              إعادة تعيين كلمة المرور
            </a>
          </p>
        </div>
      </div>
    </>
  )
}
