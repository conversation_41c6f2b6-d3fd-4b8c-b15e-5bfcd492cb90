"use client"

import { Share2 } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"

export default function ShareButton() {
  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          url: window.location.href,
        })
        // eslint-disable-next-line
      } catch (error) {
        console.error("خطأ أثناء المشاركة")
      }
    } else {
      alert("المشاركة غير مدعومة على هذا المتصفح.")
    }
  }

  return (
    <Button onClick={handleShare} variant="outlineDark">
      مشاركة <Share2 />
    </Button>
  )
}
