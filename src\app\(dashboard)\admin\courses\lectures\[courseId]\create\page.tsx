import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles";
import { lectureSchema } from "@/components/dashboard-workspace/lectures-CRUD/lectureSchema";
import LecturesForm from "@/components/dashboard-workspace/lectures-CRUD/LecturesForm";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import { notFound } from "next/navigation";
import prisma from "@/lib/prisma";
import { revalidatePath } from "next/cache";

type Props = {
  params: Promise<{ courseId: string }>;
};

export default async function CreateLecturesPage(props: Props) {
  const { courseId } = await props.params;
  const course = await prisma.course.findUnique({ where: { id: courseId } });
  if (!course) return notFound();

  return (
    <PageLayout
      title="إضافة محور جديد"
      description={`إضافة محور جديد للدورة "${course.title}"`}
    >
      <LecturesForm
        courseId={courseId}
        mode="create"
        onAction={async ({ data: request }) => {
          "use server";

          const { success, data, error } = lectureSchema.safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const [video, audio, pdf, posterUrl] = await Promise.all([
            handleFileUpdate(null, data.video, "courses/video/"),
            handleFileUpdate(null, data.audio, "courses/audio/"),
            handleFileUpdate(null, data.pdf, "courses/pdf/"),
            handleFileUpdate("", data.posterUrl, "courses/image/"),
          ]);

          await prisma.course.update({
            where: { id: courseId },
            data: {
              modulesCount: { increment: 1 },
              lectures: { create: { ...data, posterUrl, video, audio, pdf } },
            },
          });

          revalidatePath(`/courses`);
          revalidatePath(`/admin/courses`);
          revalidatePath(`/courses/${courseId}`);
          if (course.previewInHomePage) revalidatePath(`/`);
          revalidatePath(`/admin/courses/lectures/${courseId}`);
          return { success: true, message: "تم إضافة المحور بنجاح" };
        }}
      />
    </PageLayout>
  );
}

/**
 * 
 *             // Rollback all uploaded files in case of failure
            await moveFileInR2({
              fileUrl: posterNewUrl,
              moveFrom: "lectures/",
              moveTo: "tmp/",
            });

            if (videoNewUrl) {
              await moveFileInR2({
                fileUrl: videoNewUrl,
                moveFrom: "lectures/",
                moveTo: "tmp/",
              });
            }

            if (audioNewUrl) {
              await moveFileInR2({
                fileUrl: audioNewUrl,
                moveFrom: "lectures/",
                moveTo: "tmp/",
              });
            }

            if (pdfNewUrl) {
              await moveFileInR2({
                fileUrl: pdfNewUrl,
                moveFrom: "lectures/",
                moveTo: "tmp/",
              });
            }

            return { success: false, message: "حدث خطأ أثناء محاولة إضافة المحور" };
 */
