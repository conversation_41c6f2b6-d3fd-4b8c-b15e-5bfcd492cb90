"use client"

import * as React from "react"
import Link from "next/link"
import { FileTextIcon, SearchIcon, TextIcon } from "lucide-react"

import {
  getSearchQuery,
  GetSearchQueryResult,
  type SearchablePagesIds,
} from "@/lib/search-query"
import { cn } from "@/lib/utils"
import { useDebouncedCallback } from "@/hooks/use-debounced-callback"

import { Button } from "../button"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "../dialog"
import { Input } from "../input"

type SearchablePagesDetails = {
  id: SearchablePagesIds
  label: string
  path: string
}

const searchablePagesDetails: SearchablePagesDetails[] = [
  { id: "articles", label: "المقالات", path: "/articles" },
  { id: "blogPosts", label: "المدونة", path: "/blog" },
  { id: "courses", label: "الدورات تدريبية", path: "/courses" },
  { id: "interviews", label: "المقابلات", path: "/interviews" },
  { id: "books", label: "الكتب", path: "/books" },
]

const defaultSearchResult: GetSearchQueryResult = [
  { id: "courses", data: [] },
  { id: "articles", data: [] },
  { id: "blogPosts", data: [] },
  { id: "interviews", data: [] },
  { id: "books", data: [] },
]

export default function Search() {
  const [open, setOpen] = React.useState(false)
  const [searchValue, setSearchValue] = React.useState("")
  const [isPending, startTransition] = React.useTransition()
  const [searchResult, setSearchResult] =
    React.useState<GetSearchQueryResult>(defaultSearchResult)

  const handleQuery = useDebouncedCallback((value: string) => {
    startTransition(async () => {
      const result = await getSearchQuery(value)
      startTransition(() => setSearchResult(result))
    })
  }, 400)

  const onChange = React.useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value
      setSearchValue(value)
      handleQuery(value)
    },
    [handleQuery]
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="icon" className="mr-auto ml-3 h-8 w-8">
          <SearchIcon />
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-fit overflow-clip rounded-lg p-0 max-sm:top-3 max-sm:translate-y-0">
        <div className="w-[95vw] sm:w-lg">
          <Input
            value={searchValue}
            onChange={onChange}
            className="text-secondary placeholder:text-secondary/70! rounded-none! border-0! bg-white pr-6 shadow-none! ring-0! ring-offset-0!"
            placeholder="ابحث عن اي شيئ..."
          />
          <div
            className={cn(
              "border-border/30 max-h-[calc(100vh-200px)] overflow-y-auto border-t px-2 pt-1 pb-6",
              isPending && "opacity-60"
            )}
          >
            {React.useMemo(() => {
              return searchResult.length ? (
                searchResult?.map((pageSearch) => (
                  <SearchPageElement
                    key={pageSearch.id}
                    pageSearch={pageSearch}
                    setOpen={setOpen}
                  />
                ))
              ) : (
                <SearchEmpty>لا توجد نتائج</SearchEmpty>
              )
            }, [searchResult])}
          </div>
        </div>
        <DialogHeader className="sr-only">
          <DialogTitle></DialogTitle>
          <DialogDescription></DialogDescription>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  )
}

function SearchPageElement({
  pageSearch,
  setOpen,
}: {
  pageSearch: GetSearchQueryResult[number]
  setOpen: React.Dispatch<React.SetStateAction<boolean>>
}) {
  const { data, id } = pageSearch
  const { label, path } =
    searchablePagesDetails.find((page) => page.id === id) || {}

  return (
    <>
      <Link
        prefetch={false}
        href={path || "#"}
        onClick={() => setOpen(false)}
        className="flex w-full items-center gap-x-4 rounded-lg px-2 py-2.5 text-sm font-medium hover:bg-gray-100"
      >
        <FileTextIcon className="text-primary size-5 shrink-0" /> {label}
      </Link>
      <ul className="border-primary/50 mr-4 border-r pr-3">
        {data.map((item) => (
          <li key={item.id} className="my-0.5">
            <Link
              onClick={() => setOpen(false)}
              href={`${path}/${item.id}#pagination`}
              className="flex items-center gap-x-3 rounded-lg py-2.5 pr-3 text-sm text-nowrap hover:bg-gray-100"
            >
              <TextIcon className="size-5 shrink-0" />
              <span className="truncate">{item.title}</span>
            </Link>
          </li>
        ))}
      </ul>
    </>
  )
}

function SearchEmpty({ children }: { children: React.ReactNode }) {
  return <div className="py-6 text-center text-sm">{children}</div>
}
