import { revalidatePath } from "next/cache"

import prisma from "@/lib/prisma"
import FaqTable from "@/components/dashboard-workspace/faq-CRUD/FaqTable"

export default async function FaqPage() {
  const faqs = await prisma.faq.findMany({
    orderBy: { createdAt: "desc" },
    omit: { createdAt: true },
  })

  return (
    <FaqTable
      data={faqs}
      onDelete={async ({ data }) => {
        "use server"

        try {
          await prisma.faq.deleteMany({
            where: { id: { in: data.map((faq) => faq.id) } },
          })

          revalidatePath("/faq")
          revalidatePath("/admin/faq")

          return { success: true, message: "تم حذف الأسئلة بنجاح" }
        } catch (error) {
          console.error("Error deleting FAQs:", error)
          return { success: false, message: "حدث خطأ أثناء حذف الأسئلة" }
        }
      }}
    />
  )
}
