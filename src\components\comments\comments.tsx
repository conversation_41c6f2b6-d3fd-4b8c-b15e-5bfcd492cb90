import { EntityType } from "@prisma/client"

import { AddCommentForm } from "./add-comment-form"
import { CommentCard } from "./comment-card"
import { MoreComments } from "./more-comments"
import { getComments } from "./querys"

type CommentProps = {
  entity: EntityType
  entityId: string
  pathRevalidate: string
}

/**
 * مكون يعرض تعليقات المستخدمين على صفحة معينة ومنشور معين
 * يقوم بجلب أول 10 تعليقات في الخادم
 * يقوم بجلب باقي التعليقات في العميل باستخدام التمرير الانهائي
 */
export default async function Comments({
  entity,
  entityId,
  pathRevalidate,
}: CommentProps) {
  const { data, pagination } = await getComments({ entity, entityId, page: 1 })
  const hasMore = pagination.totalPages > pagination.currentPage

  return (
    <div className="relative">
      <AddCommentForm
        entity={entity}
        entityId={entityId}
        pathRevalidate={pathRevalidate}
      />

      {data.length ? (
        <div>
          {data.map((comment) => (
            <CommentCard
              usingAnimate
              key={comment.id}
              comment={comment}
              pathRevalidate={pathRevalidate}
            />
          ))}

          {hasMore && (
            <MoreComments
              entity={entity}
              entityId={entityId}
              pathRevalidate={pathRevalidate}
            />
          )}
        </div>
      ) : (
        <p className="pr-16 text-xs md:text-sm">
          لا يوجد تعليقات حتى الآن، كن أنت أول من يضيف تعليق
        </p>
      )}
    </div>
  )
}
