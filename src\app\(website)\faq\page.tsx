import { Metadata } from "next"
import Link from "next/link"
import { getFaqs } from "@/lib/querys"
import { siteName } from "@/configs/site"
import { Faq } from "@prisma/client"
import {
  ArrowRightIcon,
  HelpCircleIcon,
  MessageCircleIcon,
  SparklesIcon,
} from "lucide-react"

import { cn } from "@/lib/utils"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import MotionElement from "@/components/ui/motion-element"

export const metadata: Metadata = {
  title: `الأسئلة الشائعة | ${siteName}`,
  description:
    "إجابات شاملة على الأسئلة الأكثر شيوعاً حول خدمات الدكتورة ناهد باشطح والعلاج الشعوري. تعرف على تقنيات العلاج، الاستشارات النفسية، وكيفية التحرر من المشاعر المكبوتة",

  openGraph: {
    title: `الأسئلة الشائعة | ${siteName}`,
    description:
      "إجابات شاملة على الأسئلة الأكثر شيوعاً حول خدمات الدكتورة ناهد باشطح والعلاج الشعوري",
    type: "website",
    locale: "ar_SA",
  },
  twitter: {
    card: "summary_large_image",
    title: `الأسئلة الشائعة | ${siteName}`,
    description:
      "إجابات شاملة على الأسئلة الأكثر شيوعاً حول خدمات الدكتورة ناهد باشطح والعلاج الشعوري",
    creator: "ناهد باشطح",
  },
}

export default async function FaqPage() {
  const faqs = await getFaqs()

  // Structured data for FAQ
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: faqs.map((faq) => ({
      "@type": "Question",
      name: faq.question,
      acceptedAnswer: {
        "@type": "Answer",
        text: faq.answer,
      },
    })),
  }

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(faqStructuredData),
        }}
      />

      <div className="from-background to-background/95 min-h-screen bg-gradient-to-b">
        {/* Header Section */}
        <div className="from-primary/10 via-primary/5 relative overflow-hidden bg-gradient-to-r to-transparent pt-10">
          <div className="relative mx-auto max-w-4xl px-4 py-16 text-center sm:px-6 lg:px-8">
            <div className="bg-primary/10 mb-6 inline-flex items-center justify-center rounded-full p-3">
              <HelpCircleIcon className="text-primary h-8 w-8" />
            </div>

            <h1 className="text-secondary mb-4 text-4xl font-bold tracking-tight sm:text-5xl lg:text-6xl">
              الأسئلة الشائعة
            </h1>

            <p className="text-muted mx-auto max-w-2xl text-lg sm:text-xl">
              إجابات شاملة على الأسئلة الأكثر شيوعاً حول خدمات الدكتورة ناهد
              باشطح والعلاج الشعوري لمساعدتك في العثور على ما تبحث عنه
            </p>
          </div>
        </div>

        {/* FAQ Content */}
        <div className="mx-auto max-w-4xl px-4 py-12 sm:px-6 lg:px-8">
          {faqs.length > 0 ? (
            <div className="flex w-full flex-col gap-y-6">
              <div className="mb-8 text-center">
                <div className="bg-primary/5 text-primary inline-flex animate-pulse items-center gap-2 rounded-full px-4 py-2 text-sm font-medium">
                  <SparklesIcon className="h-4 w-4" />
                  {faqs.length} أسئلة شائعة
                </div>
              </div>

              <Accordion
                dir="rtl"
                type="single"
                collapsible
                className="flex flex-col gap-y-4"
              >
                {faqs.map((faq, index) => (
                  <FaqAccordionItem key={faq.id} index={index} faq={faq} />
                ))}
              </Accordion>
            </div>
          ) : (
            <div className="py-16 text-center">
              <div className="bg-muted/20 mb-6 inline-flex items-center justify-center rounded-full p-4">
                <HelpCircleIcon className="text-muted h-12 w-12" />
              </div>
              <h3 className="text-secondary mb-2 text-xl font-semibold">
                لا توجد أسئلة شائعة حالياً
              </h3>
              <p className="text-muted">
                سيتم إضافة الأسئلة الشائعة قريباً لمساعدتك في العثور على
                الإجابات التي تحتاجها
              </p>
            </div>
          )}
        </div>

        {/* Contact Section */}
        <div className="from-primary/5 bg-gradient-to-r to-transparent">
          <div className="mx-auto max-w-4xl px-4 py-12 text-center sm:px-6 lg:px-8">
            <h2 className="text-secondary mb-4 text-2xl font-bold">
              لم تجد إجابة لسؤالك؟
            </h2>
            <p className="text-muted mb-6">
              لا تتردد في التواصل معنا للحصول على المساعدة التي تحتاجها
            </p>
            <Link
              href="/consultation"
              className="group bg-primary text-primary-foreground hover:bg-primary/90 inline-flex items-center gap-2 rounded-lg px-6 py-3 text-sm font-medium shadow-md transition-all hover:scale-105 hover:shadow-lg"
            >
              <MessageCircleIcon className="h-4 w-4" />
              احجز استشارة
              <ArrowRightIcon className="h-4 w-4 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        </div>
      </div>
    </>
  )
}

function FaqAccordionItem({
  faq,
  index,
}: {
  faq: Omit<Faq, "createdAt">
  index: number
}) {
  return (
    <MotionElement
      rootMargin="0px 0px 20px 0px"
      delay={index * 100 - 70}
      classNameBase="transition-none translate-y-0"
      classNameOnHidden={
        index < 15 ? "opacity-0 translate-y-40" : "opacity-100 translate-y-0"
      }
      classNameOnView="opacity-100 transition-all ease-out duration-500  translate-y-0"
    >
      <AccordionItem
        key={faq.id}
        value={faq.id}
        className={cn(
          "group border-border/30 hover:border-primary/30 rounded-lg border bg-white/40 shadow-sm shadow-black/5 last:border-b hover:bg-white/60 hover:shadow-md hover:shadow-black/5"
        )}
      >
        <AccordionTrigger className="pr-4 pl-3 text-right hover:no-underline sm:pr-6 sm:pl-4">
          <div className="flex items-center gap-2 text-right sm:gap-3">
            <span className="bg-primary/10 text-primary flex size-5 shrink-0 items-center justify-center rounded-full text-xs font-bold sm:size-6">
              {index + 1}
            </span>
            <span className="group-data-[state=open]:text-primary font-semibold">
              {faq.question}
            </span>
          </div>
        </AccordionTrigger>

        <AccordionContent className="px-4 pb-6 sm:px-6">
          <div className="text-muted mr-7 leading-relaxed sm:mr-9">
            <div className="prose prose-sm max-w-none text-right">
              <p className="pl-4 whitespace-pre-line">{faq.answer}</p>
            </div>
          </div>
        </AccordionContent>
      </AccordionItem>
    </MotionElement>
  )
}
