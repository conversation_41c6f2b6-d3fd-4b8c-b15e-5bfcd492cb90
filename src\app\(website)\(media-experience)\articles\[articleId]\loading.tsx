import PageDivider from "@/components/ui/page-divider"
import { Skeleton } from "@/components/ui/skeleton"

export default function Loading() {
  return (
    <PageDivider
      pageContents={
        <div>
          <Skeleton className="h-10 w-60 rounded-full md:w-96" />
          <div className="mt-10">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9, 10].map((_, i) => (
              <Skeleton key={i} className="mb-4 h-5 w-full rounded-full" />
            ))}
          </div>
        </div>
      }
      titleMenu="مقابلات ذات صلة"
      menuItems={[1, 2, 3].map((_, i) => (
        <Skeleton key={i} className="mb-3 h-24 w-full" />
      ))}
    />
  )
}
