import { Suspense } from "react"
import { Metadata } from "next"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/utils/formaters"
import { getArticles } from "@/lib/querys"
import { Article } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import { Pagination } from "@/components/ui/pagination"
import { Skeleton } from "@/components/ui/skeleton"

type ArticlesProps = {
  searchParams: Promise<{ page?: string }>
}

export const metadata: Metadata = {
  title: `مقالات د. ناهد باشطح`,
  description:
    "مقالات الدكتورة ناهد باشطح في مجال العلاج الشعوري وتقنيات العلاج الشعوري",
  keywords: "مقالات, د. ناهد باشطح, العلاج الشعوري, تقنيات العلاج الشعوري",
}

// ==================================================================================
// صفحة تعرض كل المقالات باستخدام الترقيم الصفحي
// ==================================================================================
export default async function ArticlesPage(props: ArticlesProps) {
  const pageIndex = (await props.searchParams).page || "1"

  return (
    <Suspense key={pageIndex} fallback={<ArticlesPageSkeleton />}>
      <ArticlesList pageIndex={pageIndex} />
    </Suspense>
  )
}

// قمنا بفصل مكون العرض عن الصفحة
// لتجنب حظر العرض حتا يتم حل الوعد وجلب البيانات
// وبدلاً من ذلك سيتم عرض هيكل التحميل من خلال
// Suspense في مكون الصفحة
async function ArticlesList(props: { pageIndex?: string }) {
  const pageIndex = Number(props.pageIndex)
  if (isNaN(pageIndex)) return notFound()

  const { data, pagination } = await getArticles({ pageIndex })

  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>

  return (
    <>
      {data?.map((article) => (
        <ArticleCard key={article.id} article={article} />
      ))}
      <Pagination url="/articles" {...pagination} />
    </>
  )
}

// ==================================================================================
// مكون بطاقة المقالة
// يتم استخدامها في هذه الصفحة وفي صفحة المقالة
// ==================================================================================
function ArticleCard({ article }: { article: Article }) {
  return (
    <div className="text-secondary w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="space-y-2 px-4 pt-3 md:space-y-3 md:pt-5">
        <Link
          href={`/articles/${article.id}#pagination`}
          className="text-primary hover:text-primary/90 text-lg font-bold underline underline-offset-8"
        >
          {article.title}
        </Link>
        <p className="mt-3 line-clamp-3 min-h-[60px] text-sm leading-relaxed">
          {article.seoDescription}
        </p>
      </div>
      <div className="bg-muted/10 w-full rounded-sm px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <CalendarDays className="size-4" /> {formatDate(article.createdAt)}
        </span>
      </div>
    </div>
  )
}

function ArticlesPageSkeleton() {
  const array = Array.from({ length: 10 })

  return array.map((_, i) => (
    <Skeleton key={i} className="h-45 w-full rounded-md" />
  ))
}
