"use client"

import { useTransition } from "react"
import { useRouter } from "next/navigation"
import { Loader2 } from "lucide-react"
import { toast } from "sonner"

import useSession from "@/hooks/useSession"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"

import { subscribeCourseAction } from "./actions"
import { SubscriptionInstructionsPaidCourseDialog } from "./subscribe-course-dialog"

type SubscribeCourseButtonProps = {
  courseId: string
  coursePrice: number
  subscriptionInstructions: string
}

// ==================================================================================
// مكون زر اشتراك او شراء الدورة
// ==================================================================================
export function SubscribeCourseButton({
  courseId,
  coursePrice,
  subscriptionInstructions,
}: SubscribeCourseButtonProps) {
  const isPaid = coursePrice > 0
  const session = useSession()
  const [isPending, startTransition] = useTransition()
  const isSubscribed = session?.purchasesIds.includes(courseId)
  const router = useRouter()

  // تتعامل مع النقر على زر الإشتراك
  function handleSubscribe() {
    startTransition(async () => {
      if (!session) {
        const callbackUrl = `/courses/${courseId}`
        router.push(`/auth/login?callback-url=${callbackUrl}`, { scroll: true })
        return
      }
      const subscribe = await subscribeCourseAction({ courseId })

      if (!subscribe.success) {
        toast.error(subscribe.message)
        return
      }

      toast.success(subscribe.message)
      window.location.reload()
      window.scroll({ top: 0 })
      return
    })
  }

  // تمنع الزر من الضهور حتا يتم التحقق من ما اذا كان المستخدم قد اشترك في الدورة او لا
  if (session === null) {
    return (
      <Skeleton className="flex h-10 w-32 items-center justify-center">
        <Loader2 className="text-background size-6 animate-spin" />
      </Skeleton>
    )
  }

  if (!isPaid) {
    return (
      <Button
        onClick={!isSubscribed ? handleSubscribe : undefined}
        disabled={isPending || isSubscribed}
      >
        {isSubscribed ? "تم الاشتراك" : `اشتراك مجاني`}
      </Button>
    )
  }

  return (
    <SubscriptionInstructionsPaidCourseDialog
      subscriptionInstructions={subscriptionInstructions}
    >
      <Button
        disabled={isPending || isSubscribed}
        className="bg-paid text-paid-foreground"
      >
        {isSubscribed ? "تم الإشتراك" : `إشتراك ${coursePrice}$`}
      </Button>
    </SubscriptionInstructionsPaidCourseDialog>
  )
}
