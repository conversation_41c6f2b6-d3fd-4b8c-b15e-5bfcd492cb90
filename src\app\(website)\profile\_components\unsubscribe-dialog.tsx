"use client"

import { useState, useTransition } from "react"
import { formEntries } from "@/utils/form-entries"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  <PERSON><PERSON>Footer,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog"
import InputField from "@/components/ui/input-field"

import { unsubscribeAction, UnsubscribeActionParams } from "../_lib/actions"

type ActionResult = {
  state: { success: boolean; message: string }
  errors?: { password?: string[] }
}

type UnsubscribeDialogProps = {
  children: React.ReactNode
  entity: "courseOrders" | "bookOrders"
  orderId: string
}

export function UnsubscribeDialog({
  children,
  entity,
  orderId,
}: UnsubscribeDialogProps) {
  const [isPending, startTransition] = useTransition()
  const [errors, setErrors] = useState<ActionResult["errors"]>(undefined)
  const [isOpen, setOpen] = useState(false)
  
  function handleSubmitAction(e: React.FormEvent<HTMLFormElement>) {
    startTransition(async () => {
      e.preventDefault()
      const formData = new FormData(e.currentTarget)
      const data = formEntries<UnsubscribeActionParams>(formData)

      const { state, errors } = await unsubscribeAction(data)

      if (state.success) {
        toast.success(state.message)
        setOpen(false)
        setTimeout(() => window.location.reload(), 500)
        return
      }

      if (!state.success) {
        toast.error(state.message)
        setErrors(errors)
        return
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={setOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="max-h-[calc(100vh-100px)] overflow-y-auto">
        <DialogHeader className="text-right">
          <DialogTitle className="">إلغاء الاشتراك</DialogTitle>
          <DialogDescription className="sr-only">
            إلغاء الاشتراك
          </DialogDescription>
          <ul className="mr-5 list-outside list-disc text-sm *:my-1.5 *:text-sm *:leading-normal *:text-balance">
            <li>إلغاء الاشتراك نهائي ولا يمكن التراجع عنه</li>
            <li>لن تتمكن من استعادة الوصول إلى المحتوى.</li>
            <li>
              إذا كنت قد دفعت رسوم الاشتراك، فلن يكون هناك أي استرداد للمبلغ
              المدفوع.
            </li>
          </ul>
          <p className="mt-4 text-sm">
            إذا كنت متأكدًا من قرارك، يرجى إدخال كلمة مرور حسابك أدناه ثم النقر
            على (متابعة) لإكمال عملية الإلغاء.
          </p>
        </DialogHeader>
        <form onSubmit={handleSubmitAction}>
          <InputField
            required
            dir="ltr"
            name="password"
            label="كلمة المرور"
            placeholder="ادخل كلمة المرور"
            autoComplete="current-password"
            errorMessage={errors?.password?.at(0)}
            disabled={isPending}
          />
          <input type="hidden" name="entity" value={entity} />
          <input type="hidden" name="orderId" value={orderId} />
          <DialogFooter className="mt-6 gap-2">
            <DialogClose asChild>
              <Button type="button" variant="outline" disabled={isPending}>
                إلغاء
              </Button>
            </DialogClose>
            <Button type="submit" disabled={isPending}>
              متابعة
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
}
