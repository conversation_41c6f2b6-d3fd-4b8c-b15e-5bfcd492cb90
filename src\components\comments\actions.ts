"use server"

import { revalidatePath } from "next/cache"
import { EntityType } from "@prisma/client"
import { z } from "zod"

import { verifySession } from "@/lib/auth"
import prisma from "@/lib/prisma"

import { OnAddCommentActionResult } from "./add-comment-form"
import { getComments } from "./querys"

export async function addCommentAction({
  entity,
  comment,
  entityId,
  pathRevalidate,
}: {
  comment: string
  entityId: string
  entity: EntityType
  pathRevalidate: string
}): OnAddCommentActionResult {
  // بيانات الجلسة
  const session = await verifySession()
  if (!session) return { sucess: false, message: "يجب عليك تسجيل الدخول أولاً" }

  const schema = z.object({
    comment: z
      .string()
      .trim()
      .min(1, { message: "التعليق لا يمكن أن يكون فارغًا" })
      .max(500, { message: "التعليق طويل جدًا، الحد الأقصى 500 حرف" }),
  })

  const { success, data, error } = schema.safeParse({ comment })
  if (!success) {
    return {
      sucess: false,
      errors: error?.flatten().fieldErrors.comment?.[0],
    }
  }

  // إضافة التعليق الى قاعدة البيانات
  await prisma.comment.create({
    data: {
      entity: entity,
      entityId: entityId,
      userId: session.id,
      comment: data.comment,
      userName: session.name,
    },
  })

  revalidatePath(pathRevalidate)
  return { sucess: true, message: "تم اضافة التعليق بنجاح" }
}

export async function deleteCommentAction({
  entity,
  entityId,
  commentId,
  pathRevalidate,
}: {
  entityId: string
  commentId: string
  entity: EntityType
  pathRevalidate: string
}): Promise<{ sucess: boolean; message?: string }> {
  const session = await verifySession()

  if (!session) {
    return { sucess: false, message: "يجب عليك تسجيل الدخول أولاً" }
  }

  try {
    await prisma.comment.delete({
      where: { id: commentId, entityId, entity, userId: session.id },
    })

    revalidatePath(pathRevalidate)
    return { sucess: true, message: "تم حذف التعليق بنجاح" }

    // eslint-disable-next-line
  } catch (error) {
    return {
      sucess: false,
      message:
        "حدث خطأ. لم يتم العثور على هذا التعليق ربما قد تم حذفة من قبل تأكد من إعادة تحميل هذه الصفحة",
    }
  }
}

export async function getCommentsAction({
  entity,
  entityId,
  page,
}: {
  entity: EntityType
  entityId: string
  page: number
}) {
  return await getComments({ entity, entityId, page })
}
