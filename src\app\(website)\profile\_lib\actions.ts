"use server"

import { redirect } from "next/navigation"
import { z } from "zod"

import { comparePasswords, sessionRefresh, verifySession } from "@/lib/auth"
import prisma from "@/lib/prisma"

export type UnsubscribeActionParams = {
  password: string
  entity: "courseOrders" | "bookOrders"
  orderId: string
}

type ActionResult = {
  state: { success: boolean; message: string }
  errors?: { password?: string[] }
}

export async function unsubscribeAction(
  props: UnsubscribeActionParams
): Promise<ActionResult> {
  // التأكد من بيانات الجلسة
  const session = await verifySession()
  if (!session) redirect(`/auth/login`)

  const schema = z.object({
    password: z
      .string()
      .trim()
      .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
    entity: z.enum(["courseOrders", "bookOrders"]),
    orderId: z.string(),
  })

  const { success, data, error } = schema.safeParse(props)

  if (!success) {
    return {
      state: { success: false, message: "خطأ اثناء معالجة البيانات" },
      errors: { password: error.flatten().fieldErrors.password },
    }
  }

  const user = await prisma.user.findUnique({
    where: { id: session.id },
    select: { password: true },
  })

  if (!user) {
    return {
      state: { success: false, message: "حدث خطأ أثناء معالجة البيانات" },
    }
  }

  const verifyPassword = await comparePasswords(data.password, user.password)

  if (!verifyPassword) {
    return {
      errors: { password: ["كلمة المرور غير صحيحة"] },
      state: { success: false, message: "كلمة المرور غير صحيحة" },
    }
  }

  // يتم فصل طلب الشراء عن المستخدم
  // ولا يتم حذف الطلب من قاعدة البيانات
  await prisma.user.update({
    where: { id: session.id },
    data: { [data.entity]: { disconnect: { id: data.orderId } } },
  })

  await sessionRefresh()

  return { state: { success: true, message: "تم إلغاء الاشتراك بنجاح" } }
}
