import { Suspense } from "react"
import { redirect } from "next/navigation"
import { z } from "zod"

import { createSession, verifySession } from "@/lib/auth"
import prisma from "@/lib/prisma"
import Loading from "@/components/ui/loading"

import { ActionResult, ProfileForm } from "../_components/profile-form"

// ====================================================================================
// دالة خادم تعالج طلب تغيير البيانات الشخصية
// ====================================================================================
async function submitAction(params: { name: string }): Promise<ActionResult> {
  "use server"

  // التأكد من بيانات الجلسة
  const session = await verifySession()

  if (!session) {
    redirect(`/auth/login`)
  }

  // فحص البيانات المدخلة
  const schema = z.object({
    name: z
      .string()
      .trim()
      .min(2, "يجب أن يتكون الاسم من حرفين على الأقل.")
      .max(40, "يجب ألا يتجاوز الاسم 40 حرفًا."),
  })

  const { success, data, error } = schema.safeParse(params)

  if (!success) {
    return {
      state: { success: false, message: "خطأ اثناء معالجة البيانات" },
      errors: error.flatten().fieldErrors,
    }
  }

  // تحديث بيانات المستخدم
  const { name } = await prisma.user.update({
    where: { id: session.id },
    omit: { password: true },
    data: {
      name: data.name,
    },
  })

  // تحديث بيانات الجلسة بالبيانات الجديدة
  await createSession({ ...session, name })

  return { state: { success: true, message: "تم تعديل بياناتك بنجاح" } }
}

// ====================================================================================
// صفحة تعديل البيانات الشخصية
// ====================================================================================
export default async function PersonalInfoPage() {
  const session = await verifySession()
  if (!session) redirect("/auth/login")

  return (
    <div>
      <h1 className="text-2xl font-semibold">البيانات الشخصية</h1>
      <Suspense fallback={<Loading className="h-60" />}>
        <ProfileForm
          onSubmitAction={submitAction}
          items={[
            {
              name: "name",
              label: "الاسم",
              autoComplete: "name",
              defualtValue: session.name,
            },
          ]}
        />
      </Suspense>
    </div>
  )
}
