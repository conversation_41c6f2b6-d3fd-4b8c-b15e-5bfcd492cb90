"use client"

import React from "react"

import { cn } from "@/lib/utils"
import { useOnView } from "@/hooks/useOnScreen"

export default function MotionElement({
  delay,
  classNameOnHidden,
  classNameOnView,
  classNameBase,
  children,
  rootMargin,
  checkOnce = true,
}: {
  delay?: number
  children?: React.ReactNode
  classNameOnHidden?: string
  classNameBase?: string
  classNameOnView?: string
  rootMargin?: string
  checkOnce?: boolean
}) {
  const { isOnView, ref } = useOnView(rootMargin, checkOnce)

  return (
    <div
      ref={ref}
      style={{ transitionDelay: `${isOnView ? delay : 0}ms` }}
      className={cn(
        classNameBase,
        isOnView ? classNameOnView : classNameOnHidden
      )}
    >
      {children}
    </div>
  )
}
