import { revalidatePath, revalidateTag } from "next/cache"
import { dataLimits } from "@/configs/site"

import prisma from "@/lib/prisma"
import InterviewForm from "@/components/dashboard-workspace/interviews-CRUD/InterviewsForm"
import { interviewsSchema } from "@/components/dashboard-workspace/interviews-CRUD/InterviewsSchema"
import PageLayout from "@/components/dashboard-workspace/PageLayout"
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles"
import { convertYoutubeUrlToEmbed } from "@/components/dashboard/lib/utils"

export default function CreateInterviewPage() {
  return (
    <PageLayout title="إضافة مقابلة">
      <InterviewForm
        mode="create"
        onAction={async ({ data: request }) => {
          "use server"

          const limit = dataLimits.interviews
          const { success, data, error } = interviewsSchema
            .omit({ id: true })
            .safeParse(request)
          if (!success)
            return { success: false, errors: error.formErrors.fieldErrors }

          const thumbnail = await handleFileUpdate(
            "",
            data.thumbnail,
            "interviews/image/"
          )

          const [totalCount] = await prisma.$transaction([
            prisma.interview.count(),
            prisma.interview.create({
              data: {
                ...data,
                thumbnail,
                videoUrl: convertYoutubeUrlToEmbed(data.videoUrl),
              },
            }),
          ])

          const totalPages = Math.ceil(totalCount / limit)

          revalidateTag("pageTitlesAndIds")
          revalidatePath("/admin/interviews")
          revalidateTag("interviewsPagesCount")
          for (let page = 1; page <= totalPages; page++)
            revalidatePath(`/media-experience/interviews/${page}`)

          return { success: true, message: "تم اضافة المقابلة بنجاح" }
        }}
      />
    </PageLayout>
  )
}
