import { siteName } from "@/configs/site";
import { LoginForm } from "./login-form";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: `تسجيل الدخول | ${siteName}`,
  description: "قم بتسجيل الدخول الى حسابك الخاص او قم بإنشاء حساب جديد",
  keywords: [
    "تسجيل",
    "الدخول",
    "اشتراك",
    "إنشاء حساب",
    "تسجيل الدخول",
    "إنشاء حساب جديد",
    "اكاديمية د. ناهد باشطح",
  ],
};

export default function LoginPage() {
  return (
    <div className="flex w-full items-center justify-center">
      <LoginForm />
    </div>
  );
}
