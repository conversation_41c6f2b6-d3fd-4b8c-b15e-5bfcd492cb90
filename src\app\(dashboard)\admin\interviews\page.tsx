import { revalidatePath, revalidateTag } from "next/cache"

import { revalidateInterviews } from "@/lib/cache-invalidation"
import prisma from "@/lib/prisma"
import InterviewsTable from "@/components/dashboard-workspace/interviews-CRUD/InterviewsTable"
import { deleteFileInR2 } from "@/components/dashboard/components/fileUploader/handleFiles"

export default async function InterviewsPage() {
  const interviews = await prisma.interview.findMany({
    orderBy: { createdAt: "desc" },
  })

  return (
    <InterviewsTable
      data={interviews}
      onDelete={async ({ data }) => {
        "use server"

        await prisma.interview.deleteMany({
          where: { id: { in: data.map((interview) => interview.id) } },
        })

        await Promise.all(
          data.map((interview) =>
            deleteFileInR2({ fileUrl: interview.thumbnail })
          )
        )

        revalidateTag("pageTitlesAndIds")
        revalidatePath("/admin/interviews")
        revalidatePath("/(website)/media-experience/interviews", "layout")
        revalidateTag("interviewsPagesCount")
        await revalidateInterviews()

        return { success: true, message: "تم الحذف بنجاح" }
      }}
    />
  )
}
