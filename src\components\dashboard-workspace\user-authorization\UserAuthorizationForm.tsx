"use client";

import { Button } from "@/components/dashboard/components/ui/Button";
import { Input } from "@/components/dashboard/components/ui/Input";
import { useState } from "react";
import { toast } from "sonner";
import UserAuthorizationTable from "./UserAuthorizationTable";
import CourseSelector from "./CourseSelector";

type User = {
  id: string;
  name: string;
  email: string;
  courseOrders: {
    id: string;
    courseId: string;
    course: {
      id: string;
      title: string;
      price: number;
    };
  }[];
};

type SearchResult = {
  success: boolean;
  message: string;
  data?: User[];
};

type AuthorizeResult = {
  success: boolean;
  message: string;
};

type UserAuthorizationFormProps = {
  onSearch: (params: { data: { searchTerm: string } }) => Promise<SearchResult>;
  onAuthorize: (params: { data: { userId: string; courseId: string } }) => Promise<AuthorizeResult>;
};

export default function UserAuthorizationForm({ onSearch, onAuthorize }: UserAuthorizationFormProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [searchResults, setSearchResults] = useState<User[]>([]);
  const [selectedUser, setSelectedUser] = useState<string | null>(null);
  const [selectedCourse, setSelectedCourse] = useState<string | null>(null);
  const [isAuthorizing, setIsAuthorizing] = useState(false);

  const handleSearch = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!searchTerm.trim()) {
      toast.error("الرجاء إدخال اسم المستخدم أو البريد الإلكتروني للبحث");
      return;
    }

    setIsSearching(true);
    
    try {
      const result = await onSearch({ data: { searchTerm } });
      
      if (result.success && result.data) {
        setSearchResults(result.data);
        toast.success(result.message);
      } else {
        toast.error(result.message);
        setSearchResults([]);
      }
    } catch (error) {
      toast.error("حدث خطأ أثناء البحث");
      console.error(error);
    } finally {
      setIsSearching(false);
    }
  };

  const handleAuthorize = async () => {
    if (!selectedUser || !selectedCourse) {
      toast.error("يرجى تحديد المستخدم والدورة");
      return;
    }

    setIsAuthorizing(true);
    
    try {
      const result = await onAuthorize({
        data: {
          userId: selectedUser,
          courseId: selectedCourse
        }
      });
      
      if (result.success) {
        toast.success(result.message);
        // إعادة البحث لتحديث النتائج
        const searchResult = await onSearch({ data: { searchTerm } });
        if (searchResult.success && searchResult.data) {
          setSearchResults(searchResult.data);
        }
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      toast.error("حدث خطأ أثناء تصريح المستخدم");
      console.error(error);
    } finally {
      setIsAuthorizing(false);
    }
  };

  return (
    <div className="space-y-8">
      {/* نموذج البحث */}
      <form onSubmit={handleSearch} className="flex flex-col gap-4 sm:flex-row">
        <div className="flex-1">
          <Input
            type="text"
            placeholder="ابحث بالاسم أو البريد الإلكتروني"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            required={false}
          />
        </div>
        <Button type="submit" disabled={isSearching}>
          {isSearching ? "جاري البحث..." : "بحث"}
        </Button>
      </form>

      {/* نتائج البحث */}
      {searchResults.length > 0 && (
        <div className="space-y-6">
          <UserAuthorizationTable 
            users={searchResults} 
            selectedUser={selectedUser}
            onSelectUser={(userId) => setSelectedUser(userId)}
          />
          
          {selectedUser && (
            <div className="space-y-4 rounded-lg border p-4">
              <h3 className="text-lg font-medium">تصريح المستخدم للدورة</h3>
              <CourseSelector 
                selectedCourse={selectedCourse}
                onSelectCourse={(courseId) => setSelectedCourse(courseId)}
              />
              <Button 
                onClick={handleAuthorize} 
                disabled={!selectedCourse || isAuthorizing}
                className="w-full sm:w-auto"
              >
                {isAuthorizing ? "جاري التصريح..." : "تصريح المستخدم للدورة"}
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}
