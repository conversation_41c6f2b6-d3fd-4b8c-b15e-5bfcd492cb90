"use server";

import { revalidatePath } from "next/cache";

/**
 * دالة لإعادة تحديث cache الإحصائيات في لوحة الإدارة
 * يجب استدعاؤها عند إضافة أو تحديث أو حذف أي عنصر يؤثر على الإحصائيات
 */
export async function revalidateAdminDashboard() {
  revalidatePath("/admin");
}

/**
 * دالة لإعادة تحديث cache المستخدمين
 * تستدعى عند إضافة أو تحديث أو حذف مستخدم
 */
export async function revalidateUsers() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache الدورات تدريبية
 * تستدعى عند إضافة أو تحديث أو حذف دورة
 */
export async function revalidateCourses() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache المقالات
 * تستدعى عند إضافة أو تحديث أو حذف مقال
 */
export async function revalidateArticles() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache الكتب
 * تستدعى عند إضافة أو تحديث أو حذف كتاب
 */
export async function revalidateBooks() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache المقابلات
 * تستدعى عند إضافة أو تحديث أو حذف مقابلة
 */
export async function revalidateInterviews() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache منشورات المدونة
 * تستدعى عند إضافة أو تحديث أو حذف منشور
 */
export async function revalidateBlogPosts() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache الاستشارات
 * تستدعى عند إضافة أو تحديث أو حذف استشارة
 */
export async function revalidateConsultations() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache شهادات العملاء
 * تستدعى عند إضافة أو تحديث أو حذف شهادة
 */
export async function revalidateTestimonials() {
  revalidateAdminDashboard();
}

/**
 * دالة لإعادة تحديث cache المشرفين
 * تستدعى عند إضافة أو تحديث أو حذف مشرف
 */
export async function revalidateAdmins() {
  revalidateAdminDashboard();
}
