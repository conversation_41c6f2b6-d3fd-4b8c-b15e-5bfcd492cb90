import { Suspense } from "react"
import { Metada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { siteName } from "@/configs/site"
import { formatDate } from "@/utils/formaters"
import { Course, Lecture } from "@prisma/client"

import { getCourseById, getSiteSettings } from "@/lib/querys"
import BadgePrice from "@/components/ui/badge-price"
import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers"

import ShareButton from "../_components/share-button"
import { SubscribeCourseButton } from "../_components/subscribe-course-button"
import SubscribeCourseDialog from "../_components/subscribe-course-dialog"

type CoursePageProps = { params: Promise<{ courseId: string }> }

export const dynamic = "force-static"

export async function generateMetadata({
  params,
}: CoursePageProps): Promise<Metadata> {
  const { courseId } = await params
  const course = await getCourseById({ courseId })
  if (!course) return notFound()

  return {
    title: course.title,
    description: course.seoDescription,
    keywords: course.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar_AR",
      title: course.title,
      siteName: siteName,
      images: course.posterUrl,
      description: course.seoDescription,
      url: `/courses/${course.id}`,
    },
    twitter: {
      card: "summary_large_image",
      title: course.title,
      description: course.seoDescription,
      images: course.posterUrl,
      site: siteName,
      creator: "ناهد باشطح",
    },
    robots: {
      index: true,
      follow: false,
      googleBot: {
        index: true, // تم السماح لمحركات البحث في فهرسة الصفحة
        follow: false, // تم منع زحف الروبوتات لأن المحاور غير متاحه للمشاهده الى عند تسجيل الدخول
        "max-snippet": -1,
        "max-image-preview": "large",
        "max-video-preview": -1,
      },
    },
  }
}

// صحفة عرض دورة واحد فقط والمحاور الخاصة به
export default async function CoursePage({ params }: CoursePageProps) {
  const { courseId } = await params
  const course = await getCourseById({ courseId })
  if (!course) return notFound()

  return (
    <div className="pt-16">
      <HeaderCourse {...course} />
      <div className="mt-16 space-y-10 px-3">
        <div className="space-y-2">
          <div className="flex w-full flex-nowrap items-center gap-4">
            <h2 className="text-primary">وصف الدورة</h2>
            <hr className="border-primary mt-1.5 flex-1" />
          </div>
          <p className="text-muted max-w-(--breakpoint-lg) text-sm">
            {course.description}
          </p>
        </div>
        <div className="space-y-4">
          <div className="flex w-full flex-nowrap items-center gap-4">
            <h2 id="lectures" className="text-primary scroll-mt-20">
              محاور الدورة
            </h2>
            <hr className="border-primary mt-1.5 flex-1" />
          </div>
          <Suspense fallback={<div>جاري التحميل ...</div>}>
            <CourseLectures
              courseId={course.id}
              lectures={course.lectures}
              coursePrice={course.price}
            />
          </Suspense>
        </div>
      </div>
    </div>
  )
}

// يعرض المكون كل المحاور الخاصة بالدورة المحدد
async function CourseLectures({
  coursePrice,
  lectures,
}: {
  lectures: Lecture[]
  courseId: string
  coursePrice: number
}) {
  const { subscriptionInstructions } = await getSiteSettings()
  return (
    <div className="mb-16 min-h-svh">
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
        {lectures?.map((lecture) => (
          <div key={lecture.id} className="relative">
            <Link
              rel="nofollow"
              href={`/courses/${lecture.courseId}/${lecture.id}`}
              className="group border-border/10 bg-background hover:border-border/30 flex w-full flex-col space-y-2 overflow-clip rounded-lg border p-2 shadow-[0_0_15px_-6px_#00000050] lg:space-x-4"
            >
              <div className="relative aspect-video w-full overflow-clip rounded-sm object-cover">
                <Image
                  className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
                  sizes="(max-width: 640px) 50vw,(max-width: 768px) 30vw, 20vw"
                  src={lecture.posterUrl}
                  alt={lecture.title}
                  fill
                />
              </div>
              <p className="line-clamp-2 min-h-8 text-xs lg:min-h-10 lg:text-sm">
                {lecture.title}
              </p>
            </Link>
            <SubscribeCourseDialog
              courseId={lecture.courseId}
              coursePrice={coursePrice}
              subscriptionInstructions={subscriptionInstructions}
            />
          </div>
        ))}
      </div>
    </div>
  )
}

// يعرض المكون رأس صفحة الدورة ومعلوماته
async function HeaderCourse(course: Course) {
  const { subscriptionInstructions } = await getSiteSettings()

  return (
    <div className="bg-primary/30 relative overflow-clip">
      <DividerRedTop />
      <Image
        className="bg-muted top-0 right-0 bottom-0 left-0 z-0 h-full w-auto object-cover md:h-auto md:w-full"
        sizes="(max-width: 640px) 80vw, 40vw"
        priority
        src={course.posterUrl}
        alt={course.title}
        fill
      />
      <div className="text-background relative flex w-full flex-col gap-10 bg-linear-to-b from-black/80 to-black/30 px-3 py-20 backdrop-blur-[7px] md:flex-row md:bg-linear-to-l">
        <div className="bg-muted/80 relative aspect-square w-72 overflow-clip rounded-2xl">
          <Image
            className="bg-muted h-auto object-cover"
            sizes="(max-width: 640px) 80vw, 40vw"
            src={course.posterUrl}
            alt={course.title}
            fill
          />
        </div>

        {/* النصوص */}
        <div className="space-y-7 drop-shadow-[0_4px_2px_#00000050]">
          <h1 className="text-2xl font-bold">{course.title}</h1>

          {/* المعلومات */}
          <div className="space-y-2">
            <div>
              <span>عدد المحاور :</span>{" "}
              <span className="font-light">{course.modulesCount}</span>
            </div>
            <div>
              <span>السعر :</span> <BadgePrice price={course.price} />
            </div>
            <div className="flex flex-nowrap items-center">
              <span>تـ الإضافة :</span>{" "}
              <span className="font-light">{formatDate(course.createdAt)}</span>
            </div>
            {/* <div>
              <span>تـ آخر تعديل :</span> <span className="font-light">غير معروف</span>
            </div> */}
          </div>

          {/* الأزرار */}
          <div className="flex gap-3">
            <Suspense fallback={<div>جاري التحميل ...</div>}>
              <SubscribeCourseButton
                courseId={course.id}
                coursePrice={course.price}
                subscriptionInstructions={subscriptionInstructions}
              />
              {/* <SubscribeCourseDialog /> */}
            </Suspense>
            <ShareButton />
          </div>
        </div>
      </div>
      <DividerRedBottom />
    </div>
  )
}
