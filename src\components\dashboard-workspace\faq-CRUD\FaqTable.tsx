import { Faq } from "@prisma/client"

import PageLayout from "@/components/dashboard-workspace/PageLayout"
import DataTable from "@/components/dashboard/components/table/DataTable"
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types"

export default function FaqTable({
  data,
  onDelete,
}: {
  data: Omit<Faq, "createdAt">[]
  onDelete?: DataTableOnDeleteFnDef<Omit<Faq, "createdAt">>
}) {
  return (
    <PageLayout
      title="الأسئلة الشائعة"
      description="إدارة الأسئلة الشائعة التي يطرحها العملاء بشكل متكرر"
    >
      <DataTable<Omit<Faq, "createdAt">>
        data={data}
        defaultPageSize={10}
        rowActions={{
          onDelete,
          links: {
            items: [{ basePath: "/admin/faq/update", label: "تعديل" }],
          },
        }}
        columnSearch={{ columnKey: "question", placeholder: "بحث بالسؤال..." }}
        createDataButton={{
          href: "/admin/faq/create",
          label: "إضافة سؤال جديد",
        }}
        createColumns={[
          {
            accessorKey: "question",
            columnLabel: "السؤال",
          },
          {
            accessorKey: "answer",
            columnLabel: "الإجابة",
          },
        ]}
      />
    </PageLayout>
  )
}
