"use client"

import React from "react"
import { formatDate } from "@/utils/formaters"
import { Lecture } from "@prisma/client"

import DataTable from "@/components/dashboard/components/table/DataTable"
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types"

import PageLayout from "../PageLayout"
import TableArray from "../TableArray"
import TableFile from "../TableFile"
import TableImage from "../TableImage"

export default function LecturesTable({
  data,
  onDelete,
  courseId,
  courseTitle,
}: {
  data: Lecture[]
  courseId: string
  courseTitle: string
  onDelete?: DataTableOnDeleteFnDef<Lecture>
}) {
  return (
    <PageLayout
      title="محاور الدورة"
      description={`المحاور التابعة للدورة: "${courseTitle}"`}
    >
      <DataTable<Lecture>
        data={data}
        columnSearch={{ columnKey: "title", placeholder: "بحث بالاسم ..." }}
        createDataButton={{
          href: `/admin/courses/lectures/${courseId}/create`,
          label: "محور جديد",
        }}
        defaultPageSize={5}
        rowActions={{
          links: {
            items: [
              {
                label: "تعديل",
                basePath: `/admin/courses/lectures/${courseId}/update`,
              },
            ],
          },
          onDelete,
        }}
        createColumns={[
          { accessorKey: "title", columnLabel: "اسم المحور" },
          {
            accessorKey: "posterUrl",
            columnLabel: "صورة المحور",
            cell: ({ value }) => <TableImage src={value} />,
          },
          {
            accessorKey: "video",
            columnLabel: "فيديو",
            cell: ({ value }) => <TableFile fileType="video" href={value} />,
          },
          {
            accessorKey: "audio",
            columnLabel: "صوت",
            cell: ({ value }) => <TableFile fileType="audio" href={value} />,
          },
          {
            accessorKey: "pdf",
            columnLabel: "ملف PDF",
            cell: ({ value }) => <TableFile fileType="pdf" href={value} />,
          },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) => formatDate(value),
          },
          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },
          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  )
}
