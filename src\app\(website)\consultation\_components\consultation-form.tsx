"use client"

import { useActionState, useEffect } from "react"
import { Loader2, SendHorizonal } from "lucide-react"
import { toast } from "sonner"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"

type FormAction<TData = FormType> = {
  state?: TData
  success?: boolean
  message?: string
  errors?: { [k in keyof TData]?: string[] }
}

type FormType = {
  name: string
  email: string
  phone: string
  message: string
}

type ConsultationFormProps = {
  formAction: ConsultationFormAction
}

export type ConsultationFormAction = (
  state: FormAction,
  formData: FormData
) => Promise<FormAction>

export function ConsultationForm({ formAction }: ConsultationFormProps) {
  const [state, action, isPending] = useActionState(formAction, {
    state: { email: "", message: "", name: "", phone: "" },
  })

  useEffect(() => {
    if (state.success && state.message) {
      toast.success(state.message)
    }
    if (!state.success && state.message) {
      toast.error(state.message)
    }
  }, [state])

  return (
    <div className="flex-1">
      <form action={action} className="space-y-5">
        <div className="space-y-1 font-medium">
          <label htmlFor="name" className="text-muted text-sm">
            الاسم
          </label>
          <Input
            id="name"
            name="name"
            autoComplete="name"
            placeholder="مثال : محمد الحداد"
            defaultValue={state.state?.name}
            type="text"
            required
            disabled={isPending}
          />
          {state.errors?.name && (
            <p className="text-destructive text-xs">{state.errors?.name[0]}</p>
          )}
        </div>
        <div className="space-y-1 font-medium">
          <label htmlFor="email" className="text-muted text-sm">
            البريد الالكتروني
          </label>
          <Input
            id="email"
            name="email"
            autoComplete="email"
            placeholder="<EMAIL>"
            defaultValue={state.state?.email}
            type="email"
            required
            disabled={isPending}
          />
          {state.errors?.email && (
            <p className="text-destructive text-xs">{state.errors?.email[0]}</p>
          )}
        </div>
        <div className="space-y-1 font-medium">
          <label htmlFor="phone" className="text-muted text-sm">
            رقم الهاتف
          </label>
          <Input
            className="text-right"
            id="phone"
            name="phone"
            placeholder="+966 XXX XXX XXX"
            defaultValue={state.state?.phone}
            autoComplete="tel"
            type="tel"
            required
            disabled={isPending}
          />
          {state.errors?.phone && (
            <p className="text-destructive text-xs">{state.errors?.phone[0]}</p>
          )}
        </div>
        <div className="space-y-1 font-medium">
          <label htmlFor="message" className="text-muted text-sm">
            الرســالة
          </label>
          <Textarea
            className="text-right"
            id="message"
            name="message"
            placeholder="اكتب رسالتك هنا"
            defaultValue={state.state?.message}
            autoComplete="message"
            required
            disabled={isPending}
          />
          {state.errors?.message && (
            <p className="text-destructive text-xs">
              {state.errors?.message[0]}
            </p>
          )}
        </div>
        <div>
          <Button disabled={isPending} className="mt-5 w-full">
            إرسال
            {isPending ? (
              <Loader2 className="animate-spin" />
            ) : (
              <SendHorizonal className="rotate-180" />
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
