import "server-only"

import { cache } from "react"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import { User as PrismaUser } from "@prisma/client"
import { compare, hash } from "bcryptjs"
import { jwtVerify, SignJWT } from "jose"

import prisma from "@/lib/prisma"

const secretKey = process.env.SESSION_SECRET
const encodedKey = new TextEncoder().encode(secretKey)

type User = Omit<
  PrismaUser,
  "password" | "verificationToken" | "verificationTokenExpiry" | "emailVerified"
>

export type UserSession = User & {
  purchasesIds: string[]
}

//
// --------------------------------------------------------------------------------------
// # Encrypt
// --------------------------------------------------------------------------------------
//

/**
 * دالة تقوم بتوقيع البيانات الخاصة بالمستخدم
 *
 * @param payload UserSession
 * @returns Promise<string>
 */
async function encrypt(payload: UserSession) {
  return new SignJWT(payload)
    .setProtectedHeader({ alg: "HS256" })
    .setIssuedAt()
    .setExpirationTime("365d")
    .sign(encodedKey)
}

//
// --------------------------------------------------------------------------------------
// # Decrypt
// --------------------------------------------------------------------------------------
//

/**
 * دالة تقوم بتحقق من صحة توقيع الجلسة وفك تشفيرها
 *
 * @param session string | undefined
 * @returns Promise<UserSession | undefined>
 */
async function decrypt(session: string | undefined = "") {
  try {
    const { payload } = await jwtVerify(session, encodedKey, {
      algorithms: ["HS256"],
    })
    return payload

    // eslint-disable-next-line
  } catch (error) {
    return
  }
}

//
// --------------------------------------------------------------------------------------
// # Create Session
// --------------------------------------------------------------------------------------
//

/**
 * دالة تقوم بانشاء جلسة جديدة للمستخدم
 *
 * @param user UserSession
 * @returns Promise<void>
 */
export async function createSession(user: UserSession) {
  const expiresAt = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)
  const session = await encrypt(user)
  const cookieStore = await cookies()

  cookieStore.set("session", session, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    expires: expiresAt,
    sameSite: "lax",
    path: "/",
  })
}

//
// --------------------------------------------------------------------------------------
// # Delete Session
// --------------------------------------------------------------------------------------
//

/**
 * دالة عند تشغيلها تقوم بحذف الجلسة من الكوكيز
 *
 * @returns Promise<void>
 */
export async function deleteSession() {
  const cookieStore = await cookies()
  cookieStore.delete("session")
}

//
// --------------------------------------------------------------------------------------
// # Verify Session
// --------------------------------------------------------------------------------------
//

/**
 * دالة عند تشغيلها تقوم بالعمل بالخطوات التالية:
 * 1. تتحقق من وجود جلسة في الكوكيز
 *  - اذا لم يكن هناك جلسة في الكوكيز ترجع undefined
 *  - اذا كان هناك جلسة في الكوكيز ستكمل العمل الى الخطوة التالية
 *
 * 2. تتحقق من صحة توقيع الجلسة
 *  - اذا كان التوقيع غير صالح ترجع undefined
 *  - اذا كان التوقيع صالح ترجع بيانات الجلسة
 *
 * @returns Promise<UserSession | undefined>
 */
export const verifySession = cache(async () => {
  const cookie = (await cookies()).get("session")?.value
  const session = await decrypt(cookie)

  return session as UserSession | undefined
})

//
// --------------------------------------------------------------------------------------
// # Hash Password
// --------------------------------------------------------------------------------------
//

/**
 * دالة تقوم بتشفير كلمة المرور
 *
 * @param password string
 * @returns Promise<string>
 */
export async function hashPassword(password: string) {
  const saltRounds = 10
  const hashedPassword = await hash(password, saltRounds)
  return hashedPassword
}

//
// --------------------------------------------------------------------------------------
// # Compare Passwords
// --------------------------------------------------------------------------------------
//

/**
 * دالة تقوم بمقارنة كلمة المرور المدخلة مع كلمة المرور المخزنة (المشفرة) في قاعدة البيانات
 * اذا كانت كلمة المرور المدخلة تطابق كلمة المرور المخزنة ترجع true
 * واذا لم تتطابق ترجع false
 *
 * @param password string
 * @param hashedPassword string
 * @returns Promise<boolean>
 */
export async function comparePasswords(
  password: string,
  hashedPassword: string
) {
  const isMatch = await compare(password, hashedPassword)
  return isMatch
}

//
// --------------------------------------------------------------------------------------
// # Session Refresh
// --------------------------------------------------------------------------------------
//

/**
 * دالة تقوم بتحديث بيانات المستخدم في الجلسة
 * وتحديث بيانات المشتريات الخاصة به
 * وانشاء جلسة جديدة للمستخدم
 *
 * @returns Promise<void>
 */
export async function sessionRefresh() {
  const cookie = (await cookies()).get("session")?.value
  const session = (await decrypt(cookie)) as UserSession | undefined

  if (!session) return

  const user = await prisma.user.findUnique({
    where: { id: session.id, emailVerified: true },
    select: {
      id: true,
      name: true,
      email: true,
      bookOrders: { where: { status: "PAID" }, select: { bookId: true } },
      courseOrders: { where: { status: "PAID" }, select: { courseId: true } },
    },
  })

  // اذا كان المستخدم غير موجود في قاعدة البيانات
  // يتم حذف الجلسة التي يستخدمها وإعادة توجيهه الى صفحة تسجيل الدخول
  if (!user) {
    await deleteSession()
    redirect("/auth/login")
  }

  // انشاء مصفوفة لمعرفات كل الدورات تدريبية والكتب التي اشترك فيها المستخدم
  const userBooksIds = user.bookOrders.map((bookOrder) => bookOrder.bookId)
  const userCoursesIds = user.courseOrders.map(
    (courseOrder) => courseOrder.courseId
  )

  const purchasesIds = [...userBooksIds, ...userCoursesIds]

  // انشاء جلسة جديدة للمستخدم
  await createSession({
    id: user.id,
    purchasesIds,
    name: user.name,
    email: user.email,
  })
}
