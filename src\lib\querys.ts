import { cache } from "react"
import { unstable_cache } from "next/cache"
import { notFound } from "next/navigation"
import { dataLimits } from "@/configs/site"
import { SiteSettings } from "@prisma/client"

import { verifySession } from "@/lib/auth"
import prisma from "@/lib/prisma"

export const getTestimonials = async () => {
  return await prisma.testimony.findMany()
}

export const getHomeCourses = async () => {
  return await prisma.course.findMany({
    where: { previewInHomePage: true },
    orderBy: { createdAt: "desc" },
  })
}

export const getCourses = async () => {
  return await prisma.course.findMany({ orderBy: { createdAt: "desc" } })
}

export const getFaqs = async () => {
  return await prisma.faq.findMany({
    orderBy: { createdAt: "desc" },
    select: {
      id: true,
      question: true,
      answer: true,
    },
  })
}

export const getCoursesWithLectures = async () => {
  return await prisma.course.findMany({
    orderBy: { createdAt: "desc" },
    include: { lectures: { orderBy: { createdAt: "asc" } } },
  })
}

export const getCourseById = cache(
  async ({ courseId }: { courseId: string }) => {
    return await prisma.course.findUnique({
      where: { id: courseId },
      include: { lectures: { orderBy: { createdAt: "asc" } } },
    })
  }
)

export const getCourseLectures = cache(
  async ({ courseId }: { courseId: string }) => {
    return await prisma.lecture.findMany({
      orderBy: { createdAt: "desc" },
      where: { courseId },
    })
  }
)

export const getArticles = cache(
  async ({ pageIndex }: { pageIndex: number }) => {
    const limit = dataLimits.articles
    const skip = (pageIndex - 1) * limit

    const [data, totalCount] = await prisma.$transaction([
      prisma.article.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        skip,
      }),
      prisma.article.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    if (pageIndex > totalPages) return notFound()

    return {
      data,
      pagination: {
        currentPage: pageIndex,
        totalPages,
      },
    }
  }
)

export const getBooks = cache(async ({ pageIndex }: { pageIndex: number }) => {
  if (!pageIndex) return notFound()
  const limit = dataLimits.books
  const skip = (pageIndex - 1) * limit

  const [data, totalCount] = await prisma.$transaction([
    prisma.book.findMany({
      orderBy: { createdAt: "desc" },
      take: limit,
      skip,
    }),
    prisma.book.count(),
  ])

  const totalPages = Math.ceil(totalCount / limit)
  if (pageIndex > totalPages) return notFound()

  return {
    data,
    pagination: {
      currentPage: pageIndex,
      totalPages,
    },
  }
})

export const getInterviews = cache(
  async ({ pageIndex }: { pageIndex: number }) => {
    if (!pageIndex) return notFound()
    const limit = dataLimits.interviews
    const skip = (pageIndex - 1) * limit

    const [data, totalCount] = await prisma.$transaction([
      prisma.interview.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        skip,
      }),
      prisma.interview.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)
    if (pageIndex > totalPages) return notFound()

    return {
      data,
      pagination: {
        currentPage: pageIndex,
        totalPages,
      },
    }
  }
)

export const getBlogPosts = cache(
  async ({ pageIndex }: { pageIndex: number }) => {
    if (!pageIndex) return notFound()
    const limit = dataLimits.blogPost
    const skip = (pageIndex - 1) * limit

    const [data, totalCount] = await prisma.$transaction([
      prisma.blogPost.findMany({
        orderBy: { createdAt: "desc" },
        take: limit,
        skip,
      }),
      prisma.blogPost.count(),
    ])

    const totalPages = Math.ceil(totalCount / limit)

    return {
      data,
      pagination: {
        currentPage: pageIndex,
        totalPages,
      },
    }
  }
)

export const getUser = cache(async () => {
  const session = await verifySession()

  if (!session) return null

  const user = await prisma.user.findUnique({
    where: { email: session?.email },
    include: {
      bookOrders: { where: { status: "PAID" }, include: { book: true } },
      courseOrders: { where: { status: "PAID" }, include: { course: true } },
    },
  })

  if (!user) return null

  return user
})

export const getSiteSettings = unstable_cache(
  async (): Promise<SiteSettings> => {
    const siteSettings = await prisma.siteSettings.findUnique({
      where: { id: 1 },
    })

    if (!siteSettings) {
      return {
        id: 1,
        phone: "",
        email: "",
        facebookUrl: "",
        instagramUrl: "",
        twitterUrl: "",
        youtubeUrl: "",
        tiktokUrl: "",
        myStoryVideoUrl: "",
        myStoryVideoThumbnailUrl: "",
        subscriptionInstructions: "",
      }
    }

    return siteSettings
  },
  [],
  {
    tags: ["siteSettings"],
  }
)

/**
 * ترجع titles و ids للصفحات التالية:
 * - المدونة
 * - المقالات
 * - المقابلات
 * - الكتب
 * - الدورات تعليمية
 */
export const getPageTitlesAndIds = unstable_cache(
  async () => {
    const getCourses = () => {
      return prisma.course.findMany({ select: { id: true, title: true } })
    }
    const getBlogPosts = () => {
      return prisma.blogPost.findMany({ select: { id: true, title: true } })
    }
    const getArticles = () => {
      return prisma.article.findMany({ select: { id: true, title: true } })
    }
    const getInterviews = () => {
      return prisma.interview.findMany({ select: { id: true, title: true } })
    }
    const getBooks = () => {
      return prisma.book.findMany({ select: { id: true, title: true } })
    }
    const [courses, blogPosts, articles, interviews, books] = await Promise.all(
      [getCourses(), getBlogPosts(), getArticles(), getInterviews(), getBooks()]
    )

    return [
      { id: "courses", data: courses },
      { id: "articles", data: articles },
      { id: "blogPosts", data: blogPosts },
      { id: "interviews", data: interviews },
      { id: "books", data: books },
    ]
  },
  [],
  {
    tags: ["pageTitlesAndIds"],
  }
)
