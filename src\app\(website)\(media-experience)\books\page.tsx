import { Suspense } from "react"
import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/utils/formaters"
import { getBooks } from "@/lib/querys"
import { Book } from "@prisma/client"
import { BookOpenText, CalendarDays } from "lucide-react"

import { Pagination } from "@/components/ui/pagination"
import { Skeleton } from "@/components/ui/skeleton"

type BooksPageProps = { searchParams: Promise<{ page?: string }> }

export const metadata: Metadata = {
  title: `كتب د. ناهد باشطح`,
  description: "كتب ومؤلفات الدكتورة ناهد باشطح",
  keywords: "كتب, ناهد باشطح, العلاج, مؤلفات",
}

// ==================================================================================
// صفحة تعرض جميع الكتب
// ==================================================================================
export default async function BooksPage(props: BooksPageProps) {
  const pageIndex = (await props.searchParams).page || "1"

  return (
    <>
      <Suspense key={pageIndex} fallback={<BooksPageSkeleton />}>
        <BooksList pageIndex={pageIndex} />
      </Suspense>
    </>
  )
}

async function BooksList(props: { pageIndex?: string }) {
  const pageIndex = Number(props.pageIndex)
  if (isNaN(pageIndex)) return notFound()

  const { data, pagination } = await getBooks({ pageIndex })
  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>

  return (
    <>
      {data?.map((book) => (
        <BookCard key={book.id} book={book} />
      ))}
      <Pagination url="/books" {...pagination} />
    </>
  )
}

// ==================================================================================
// مكون بطاقة الكتاب
// ==================================================================================
function BookCard({ book }: { book: Book }) {
  return (
    <div className="w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="flex gap-3 px-4 pt-3 md:pt-5">
        <div className="border-muted/0 bg-muted relative aspect-[1/1.5] w-16 shrink-0 overflow-clip rounded-md border sm:w-20">
          <Image
            sizes="(max-width: 768px) 30vw, 7vw"
            src={book.coverImage}
            className="h-auto w-full object-cover"
            alt={book.title}
            fill
          />
        </div>
        <div className="space-y-2 md:space-y-3">
          <Link
            href={`/books/${book.id}`}
            className="text-primary hover:text-primary/90 text-lg font-bold underline underline-offset-7"
          >
            {book.title}
          </Link>
          <p className="mt-3 line-clamp-3 min-h-[60px] text-sm leading-relaxed">
            {book.summary} {book.summary} {book.summary} {book.summary}
          </p>
        </div>
      </div>
      <div className="bg-muted/10 flex w-full gap-2 rounded-sm px-4 py-2">
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <CalendarDays className="size-4" />{" "}
          <span className="w-full max-w-32 truncate">
            {formatDate(book.createdAt)}
          </span>
        </span>
        <div className="bg-muted/30 min-h-max w-px" />
        <span className="flex flex-nowrap items-center gap-2 text-xs text-nowrap">
          <BookOpenText className="size-4" />{" "}
          <span className="w-full max-w-32 truncate">
            {book.pagesCount} صفحة
          </span>
        </span>
      </div>
    </div>
  )
}

function BooksPageSkeleton() {
  const array = Array.from({ length: 4 })

  return array.map((_, i) => (
    <Skeleton key={i} className="h-48 w-full rounded-md" />
  ))
}
