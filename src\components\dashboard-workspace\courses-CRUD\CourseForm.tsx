import DataForm from "@/components/dashboard/components/form/DataForm";
import { DataFormOnActionDef } from "@/components/dashboard/components/form/types";
import { Course } from "@prisma/client";

export default function CourseForm({
  mode,
  defaultValues,
  onAction,
}: {
  mode: "create" | "update";
  defaultValues?: Partial<Course>;
  onAction?: DataFormOnActionDef<Course>;
}) {
  return (
    <DataForm<Course>
      mode={mode}
      defaultValues={defaultValues}
      fields={[
        { accessorKey: "title", label: "اسم الدورة", placeholder: "ادخل اسم الدورة" },
        {
          accessorKey: "description",
          label: "الوصف",
          fieldType: "textarea",
          placeholder: "اكتب وصف لهذا الدورة",
        },
        {
          accessorKey: "posterUrl",
          label: "صورة الدورة",
          fieldType: "fileUploader",
          description: "احرص على ان تكون الوان الصورة مناسبة مع ثيم الموقع",
        },

        {
          accessorKey: "features",
          label: "مميزات الدورة",
          fieldType: "inputArray",
          placeholder: "ادخل ميزة ...",
          description: "يجب ان يحتوي هذا الحقل على 6 قيم, حاول ان تجعل كل قيمة لا تتجاوز 3 كلمات",
        },
        {
          label: "وصف محركات البحث",
          accessorKey: "seoDescription",
          fieldType: "textarea",
          placeholder: "وصف الدورة لمحركات البحث ...",
          description:
            "وصف مختصر يظهر في نتائج محركات البحث. يساعد في تحسين ظهور الصفحة وجذب الزوار. يُفضل أن يكون جذابًا ولا يتجاوز 160 حرفًا.",
        },
        {
          accessorKey: "seokeywords",
          label: "الكلمات المفتاحية",
          fieldType: "inputArray",
          placeholder: "كلمة مفتاحية...",
          description: "اكتب كلمات أو عبارات تعتقد أن الناس سيبحثون بها للوصول إلى هذه النوع من المحتوى.",
        },
        {
          label: "السعر",
          accessorKey: "price",
          placeholder: "$0.00",
          required: false,
          description: `سعر الدورة بالدولار. عندما يترك فارغ او ادخال قيمة تساوي "0" سيكون الدورة مجاني`,
        },
        {
          accessorKey: "previewInHomePage",
          label: "عرض في الرئيسية",
          fieldType: "checkbox",
          required: false,
          description: "إذا كان هذا مفعلاً سيتم عرض هذا الدورة على الصفحة الرئيسية.",
        },

        // حقول مخفية
        // { accessorKey: "id", label: "", fieldType: "hidden" },
        { accessorKey: "modulesCount", label: "", fieldType: "hidden" },
      ]}
      actionAndStartOverButtonLabel="إضافة وبدء من جديد"
      actionButtonLabel={mode === "create" ? "إضافة" : "تعديل"}
      callbackUrl="/admin/courses"
      cancelButtonLabel="إلغاء"
      onAction={onAction}
    />
  );
}
