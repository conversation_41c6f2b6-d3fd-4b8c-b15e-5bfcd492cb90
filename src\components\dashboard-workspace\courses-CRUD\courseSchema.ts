import { z } from "zod";

export const courseSchema = z.object({
  title: z.string().trim(),
  price: z
    .number()
    .or(
      z
        .string()
        .trim()
        .optional()
        .transform((val) => (val === "" ? 0 : Number(val)))
        .pipe(z.number()),
    )
    .optional()
    .default(0),

  posterUrl: z.string().url(),
  description: z.string().trim(),

  modulesCount: z
    .string()
    .optional()
    .transform((val) => Number(val))
    .pipe(z.number().default(0)),

  previewInHomePage: z
    .preprocess((val) => val === "on", z.boolean())
    .optional()
    .default(false),
  seoDescription: z.string().trim(),
  seokeywords: z.array(z.string().trim()),
  features: z
    .array(
      z
        .string()
        .trim()
        .max(25, "لقد قمت بإدخال ميزات طويله وهذا غير مسموح. قد تسبب الميزات الطويله تشوه في تصميم الموقع"),
    )
    .min(6, "يجب ان تكون عدد ميزات الدورة 6 ميزات كحد أدنى")
    .max(6, "يجب ان تكون عدد ميزات الدورة 6 ميزات كحد أقصى"),
});
