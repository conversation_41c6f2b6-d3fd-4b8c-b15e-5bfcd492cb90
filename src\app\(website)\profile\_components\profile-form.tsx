"use client"

import { useState, useTransition } from "react"
import { formEntries } from "@/utils/form-entries"
import { Edit } from "lucide-react"
import { toast } from "sonner"

import { But<PERSON> } from "@/components/ui/button"
import InputField from "@/components/ui/input-field"

export type ActionResult = {
  state: { success: boolean; message: string }
  errors?: { [k: string]: string[] }
}

export type ProfileFieldItem = {
  name: string
  label: string
  dir?: "rtl" | "ltr"
  defualtValue?: string
  placeholder?: string
  type?: React.HTMLInputTypeAttribute
  autoComplete?: React.HTMLInputAutoCompleteAttribute
}

type ProfileFormProps = {
  onSubmitAction: (params: any) => Promise<ActionResult>
  items: ProfileFieldItem[]
}

export function ProfileForm({ onSubmitAction, items }: ProfileFormProps) {
  const [isPending, startTransition] = useTransition()
  const [isEnableEditing, setEnableEditing] = useState(false)
  const [value, setValue] = useState<{ [k: string]: string }>({})
  const [errors, setErrors] = useState<ActionResult["errors"]>(undefined)

  function handleOnChange(e: React.ChangeEvent<HTMLInputElement>) {
    setValue((prev) => ({ ...prev, [e.target.name]: e.target.value }))
  }

  function handleOnSubmit(e: React.FormEvent<HTMLFormElement>) {
    startTransition(async () => {
      e.preventDefault()
      const formData = new FormData(e.currentTarget)
      const data = formEntries<{ [K: string]: string }>(formData)

      const { state, errors } = await onSubmitAction(data)

      if (state.success) {
        toast.success(state.message)
        setEnableEditing(false)
        window.location.reload()
      }

      if (!state.success) {
        toast.error(state.message)
        setErrors(errors)
      }
    })
  }

  return (
    <div>
      <form onSubmit={handleOnSubmit} className="grid gap-6">
        <hr className="border-muted/50 mt-6 mb-3 border-dashed" />
        {items.map((item) => (
          <InputField
            dir={item.dir}
            key={item.name}
            name={item.name}
            label={item.label}
            placeholder={item.placeholder}
            errorMessage={errors?.[item.name]?.at(0)}
            autoComplete={item.autoComplete}
            value={value[item.name] ?? item.defualtValue ?? ""}
            className="max-w-lg md:col-span-3"
            containerClass="md:grid-cols-4 md:items-center"
            disabled={!isEnableEditing || isPending}
            onChange={handleOnChange}
          />
        ))}
        <hr className="border-muted/50 mt-3 border-dashed" />
        <div className="flex flex-col gap-3 md:flex-row">
          <ButtonEnableEditing
            isEnableEditing={isEnableEditing}
            onClick={() => setEnableEditing(true)}
          />
          <ButtonSave disabled={isPending} isEnableEditing={isEnableEditing} />
          <ButtonCancel
            isEnableEditing={isEnableEditing}
            disabled={isPending}
            onClick={() => {
              if (!Object.keys(value).length) {
                setEnableEditing(false)
                return
              }

              const cancel = confirm(
                "سوف يتم إعادة كل الحقول التي أجريت تعديلات عليها الى القيم الإفتراضية. انقر (حسناً) للمتابعة"
              )
              if (cancel) {
                setEnableEditing(false)
                setValue({})
              }
            }}
          />
        </div>
      </form>
    </div>
  )
}

function ButtonSave({
  isEnableEditing,
  disabled,
}: {
  isEnableEditing: boolean
  disabled: boolean
}) {
  if (!isEnableEditing) return null

  return (
    <Button disabled={disabled} type="submit">
      حفض
    </Button>
  )
}
function ButtonCancel({
  onClick,
  disabled,
  isEnableEditing,
}: {
  onClick: () => void
  disabled: boolean
  isEnableEditing: boolean
}) {
  if (!isEnableEditing) return null

  return (
    <Button
      disabled={disabled}
      type="button"
      variant="outline"
      onClick={onClick}
    >
      إلغاء
    </Button>
  )
}

function ButtonEnableEditing({
  onClick,
  isEnableEditing,
}: {
  onClick: () => void
  isEnableEditing: boolean
}) {
  if (isEnableEditing) return null

  return (
    <Button type="button" onClick={onClick}>
      تمكين التعديل <Edit />
    </Button>
  )
}
