import React from "react"
import { <PERSON>ada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { formatDate } from "@/utils/formaters"
import { siteName } from "@/configs/site"
import { Interview } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import prisma from "@/lib/prisma"
import Comments from "@/components/comments/comments"
import PageDivider from "@/components/ui/page-divider"

type InterviewPageProps = {
  params: Promise<{ interviewId: string }>
}

const getInterviewAndRelated = React.cache(async (interviewId?: string) => {
  const interviews = await prisma.interview.findMany({
    cursor: { id: interviewId },
    take: 11,
  })

  const interview = interviews.find((interview) => interview.id === interviewId)
  const related = interviews?.filter(
    (interview) => interview.id !== interviewId
  )

  if (!interview) return notFound()
  return { interview, related }
})

export async function generateMetadata(
  props: InterviewPageProps
): Promise<Metadata> {
  const bookId = (await props.params).interviewId
  const { interview } = await getInterviewAndRelated(bookId)

  return {
    title: interview.title,
    description: interview.seoDescription,
    keywords: interview.seokeywords,
    openGraph: {
      type: "website",
      locale: "ar",
      siteName: siteName,
      images: interview.thumbnail,
      description: interview.seoDescription,
      title: `${interview.title} | د. ناهد باشطح`,
      url: `/books/${interview.id}`,
    },
    twitter: {
      card: "summary_large_image",
      title: interview.title,
      description: interview.seoDescription,
      images: interview.thumbnail,
      site: siteName,
      creator: "ناهد باشطح",
    },
  }
}

// ==================================================================================
// صفحة المقابلة
// ==================================================================================
export default async function InterviewPage(props: InterviewPageProps) {
  const interviewId = (await props.params).interviewId

  const { interview, related } = await getInterviewAndRelated(interviewId)

  return (
    <PageDivider
      pageContents={
        <iframe
          className="bg-muted aspect-video h-auto w-full rounded-lg"
          src={interview.videoUrl}
          title={interview.title}
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          allowFullScreen
        />
      }
      titleMenu="مقابلات ذات صلة"
      menuItems={related?.map((interview) => (
        <InterviewMenuCard key={interview.id} interview={interview} />
      ))}
      comments={
        <Comments
          entity="interview"
          entityId={interview.id}
          pathRevalidate={`/interviews/${interview.id}`}
        />
      }
    />
  )
}

// ==================================================================================
// مكون بطاقة المقابلة في قائمة المحتوى ذات الصلة
// ==================================================================================
function InterviewMenuCard({ interview }: { interview: Interview }) {
  return (
    <Link
      href={`/interviews/${interview.id}#pagination`}
      className="group hover:from-muted/50 flex snap-start items-start gap-3 rounded-lg bg-linear-to-r to-65% py-3"
    >
      <div className="bg-muted/80 relative aspect-video w-[30%] shrink-0 overflow-clip rounded-md">
        <Image
          className="h-auto w-full scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
          sizes="(max-width: 768px) 80vw, 10vw"
          src={interview.thumbnail}
          alt={interview.title}
          fill
        />
      </div>
      <div className="w-[59%] space-y-1 rounded-lg">
        <p className="w-full truncate text-sm font-medium">{interview.title}</p>
        <p className="text-background/70 line-clamp-2 w-full text-xs">
          {interview.description}
        </p>
        <p className="text-background/70 flex gap-1 pt-1 text-[10px]">
          <CalendarDays className="size-3" /> {formatDate(interview.createdAt)}
        </p>
      </div>
    </Link>
  )
}
