"use server"

import { redirect } from "next/navigation"
import { formEntries } from "@/utils/form-entries"
import { z } from "zod"

import { hashPassword } from "@/lib/auth"
import { sendEmail } from "@/lib/email"
import prisma from "@/lib/prisma"

type SignupActionParams = Promise<{
  state: { message?: string; success: boolean }
  errors?: { name?: string[]; email?: string[]; password?: string[] }
}>

export async function signupAction(formData: FormData): SignupActionParams {
  const request = formEntries(formData)

  const schema = z.object({
    email: z.string().trim().email("الرجاء إدخال بريد إلكتروني صالح."),
    password: z
      .string()
      .trim()
      .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
    name: z
      .string()
      .trim()
      .min(2, "يجب أن يتكون الاسم من حرفين على الأقل.")
      .max(40, "يجب ألا يتجاوز الاسم 40 حرفًا."),
  })

  const { success, data, error } = schema.safeParse(request)

  if (!success) {
    return {
      state: { success, message: "البيانات المدخلة غير صحيحة" },
      errors: error?.flatten().fieldErrors,
    }
  }

  const searchEmail = await prisma.user.findUnique({
    where: { email: data.email },
  })

  if (searchEmail) {
    // البريد الإلكتروني مستخدم من قبل وقد تم التحقق منه بنجاح
    if (searchEmail.emailVerified) {
      return {
        state: { success: false, message: "البريد الالكتروني مستخدم من قبل" },
        errors: { email: ["تم استخدام هذا البريد الالكتروني من قبل"] },
      }
    }

    return {
      state: {
        success: false,
        message:
          "لا يمكنك استخدام هذا البريد الإلكتروني!!, اذا كنت قد قمت بإنشاء حساب من قبل باستخدام هذه البيانات فقم بمحاولة تسجيل الدخول بدل من انشاء حساب جديد",
      },
    }
  }

  const { isSended, verificationToken, verificationTokenExpiry } =
    await sendEmail({
      userEmail: data.email,
      userName: data.name,
    })

  if (!isSended) {
    return {
      state: {
        success: false,
        message:
          "حدث خطأ أثناء إرسال البريد الإلكتروني. يرجى المحاولة مرة أخرى.",
      },
    }
  }

  await prisma.user.create({
    data: {
      ...data,
      password: await hashPassword(data.password),
      verificationToken,
      verificationTokenExpiry,
      emailVerified: false,
    },
  })

  redirect("/auth/waiting-verify?email=" + data.email)

  // return {
  //   state: {
  //     success: true,
  //     message: "تم إنشاء حسابك بنجاح. يرجى التحقق من بريدك الإلكتروني لتفعيل حسابك.",
  //   },
  // };
}
