import prisma from "@/lib/prisma";
import PageLayout from "@/components/dashboard-workspace/PageLayout";
import ArticleForm from "@/components/dashboard-workspace/articles-CRUD/ArticlesForm";

import { articlesSchema } from "@/components/dashboard-workspace/articles-CRUD/ArticlesSchema";
import { dataLimits } from "@/configs/site";
import { revalidatePath, revalidateTag } from "next/cache";
import { revalidateArticles } from "@/lib/cache-invalidation";

export default function CreateArticlePage() {
  return (
    <PageLayout title="إضافة مقال">
      <ArticleForm
        mode="create"
        onAction={async ({ data: request }) => {
          "use server";

          const limit = dataLimits.articles;
          const { success, data, error } = articlesSchema.omit({ id: true }).safeParse(request);
          if (!success) return { success: false, errors: error.formErrors.fieldErrors };

          const [totalCount] = await prisma.$transaction([prisma.article.count(), prisma.article.create({ data })]);

          const totalPages = Math.ceil(totalCount / limit);

          revalidateTag("pageTitlesAndIds");
          revalidatePath("/admin/articles");
          revalidateTag("articlesPagesCount");
          for (let page = 1; page <= totalPages; page++) revalidatePath(`/media-experience/articles/${page}`);
          await revalidateArticles();

          return { success: true, message: "تم اضافة المقال بنجاح" };
        }}
      />
    </PageLayout>
  );
}
