import { redirect } from "next/navigation"
import { z } from "zod"

import { comparePasswords, hashPassword, verifySession } from "@/lib/auth"
import prisma from "@/lib/prisma"

import {
  ActionResult,
  ProfileFieldItem,
  ProfileForm,
} from "../_components/profile-form"

const items: ProfileFieldItem[] = [
  {
    dir: "ltr",
    type: "password",
    name: "currentPassword",
    label: "كلمة المرور الحالية",
    placeholder: "كلمة المرور الحالية",
    autoComplete: "current-password",
  },
  {
    dir: "ltr",
    type: "password",
    name: "newPassword",
    label: "كلمة المرور الجديدة",
    autoComplete: "new-password",
    placeholder: "كلمة المرور الجديدة",
  },
  {
    dir: "ltr",
    type: "password",
    label: "تأكيد كلمة المرور",
    name: "confirmPassword",
    autoComplete: "new-password",
    placeholder: "تأكيد كلمة المرور الجديدة",
  },
]

async function submitAction(params: any): Promise<ActionResult> {
  "use server"

  // التأكد من بيانات الجلسة
  const session = await verifySession()
  if (!session) redirect(`/auth/login`)

  const schema = z
    .object({
      currentPassword: z
        .string()
        .trim()
        .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
      newPassword: z
        .string()
        .trim()
        .min(6, "يجب أن تتكون كلمة المرور من 6 أحرف على الأقل."),
      confirmPassword: z.string().trim(),
    })
    .refine((data) => data.newPassword === data.confirmPassword, {
      message: "كلمتا المرور غير متطابقتين",
      path: ["confirmPassword"],
    })

  const { success, data, error } = schema.safeParse(params)

  if (!success) {
    return {
      state: { success, message: "البيانات المدخلة غير صحيحة" },
      errors: error?.flatten().fieldErrors,
    }
  }

  const user = await prisma.user.findUnique({
    where: { id: session.id },
    select: { password: true },
  })

  if (!user) redirect(`/auth/login`)

  const verifyPassword = await comparePasswords(
    data.currentPassword,
    user?.password
  )

  if (!verifyPassword) {
    return {
      state: { success: false, message: "كلمة المرور الحالية غير صحيحة" },
      errors: { currentPassword: ["كلمة المرور الحالية غير صحيحة"] },
    }
  }

  const newPassword = await hashPassword(data.newPassword)

  await prisma.user.update({
    where: { id: session.id },
    data: { password: newPassword },
  })

  return { state: { success: true, message: "تم تعديل كلمة المرور بنجاح" } }
}

// ====================================================================================
// صفحة تعديل كلمة المرور
// ====================================================================================
export default async function AccountSecurityPage() {
  const session = await verifySession()
  if (!session) redirect("/auth/login")

  return (
    <div>
      <h1 className="text-2xl font-semibold">أمان الحساب</h1>
      <ProfileForm onSubmitAction={submitAction} items={items} />
    </div>
  )
}
