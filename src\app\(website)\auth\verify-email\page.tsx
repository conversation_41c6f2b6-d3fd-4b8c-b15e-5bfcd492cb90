import { Suspense } from "react"
import { Metada<PERSON> } from "next"
import { siteName } from "@/configs/site"

import Loading from "@/components/ui/loading"

import { EmailVerificationForm } from "./email-verification-form"

export const metadata: Metadata = {
  title: `تأكيد البريد الإلكتروني | ${siteName}`,
  description: "تأكيد البريد الإلكتروني في موقع اكاديمية د. ناهد باشطح",
  robots: {
    index: false,
    follow: false,
  },
}

export default async function VerifyEmailPage({
  searchParams,
}: {
  searchParams: Promise<{ token?: string }>
}) {
  return (
    <div className="flex w-full items-center justify-center">
      <Suspense fallback={<Loading />}>
        <EmailVerificationForm token={(await searchParams).token} />
      </Suspense>
    </div>
  )
}
