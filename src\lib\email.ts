import "server-only"

import {
  consultationResponseTimeHours,
  siteName,
  verificationTokenExpiryMinutes,
} from "@/configs/site"
import nodemailer from "nodemailer"

// Create a transporter with SMTP configuration
const createTransporter = () => {
  return nodemailer.createTransport({
    host: process.env.SMTP_HOST,
    port: Number(process.env.SMTP_PORT),
    secure: process.env.SMTP_SECURE === "true",
    auth: {
      user: process.env.SMTP_USER,
      pass: process.env.SMTP_PASSWORD,
    },
  })
}

type SendEmailParams = {
  userName: string
  userEmail: string
}

/**
 * Send an email using SMTP
 */
export async function sendEmail({ userEmail, userName }: SendEmailParams) {
  try {
    const transporter = createTransporter()
    const verificationToken = generateVerificationToken()
    // استخدام المتغير المركزي لتحديد فترة انتهاء صلاحية رابط التحقق
    const verificationTokenExpiry = new Date(
      Date.now() + verificationTokenExpiryMinutes * 60 * 1000
    )
    const verificationUrl = `${process.env.DOMAIN}/auth/verify-email?token=${verificationToken}`

    const emailHtml = generateVerificationEmailTemplate({
      userName,
      verificationUrl,
    })

    const info = await transporter.sendMail({
      to: userEmail,
      html: emailHtml,
      subject: "تأكيد البريد الإلكتروني - أكاديمية د. ناهد باشطح",
      from: process.env.SMTP_FROM || "<EMAIL>",
    })

    return { isSended: true, verificationToken, verificationTokenExpiry, info }
  } catch (error) {
    return { isSended: false, error }
  }
}

/**
 * Generate a verification email HTML template
 */
function generateVerificationEmailTemplate({
  userName,
  verificationUrl,
}: {
  userName: string
  verificationUrl: string
}) {
  return `
    <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h1 style="color: #333; text-align: center;">تأكيد البريد الإلكتروني</h1>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">مرحباً ${userName}،</p>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">شكراً لتسجيلك في أكاديمية د. ناهد باشطح. يرجى النقر على الزر أدناه لتأكيد بريدك الإلكتروني:</p>
      <div style="text-align: center; margin: 30px 0;">
        <a href="${verificationUrl}" style="background-color: #4CAF50; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; font-weight: bold; display: inline-block;">تأكيد البريد الإلكتروني</a>
      </div>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">إذا لم تقم بإنشاء حساب، يمكنك تجاهل هذا البريد الإلكتروني.</p>
      <p style="font-size: 16px; line-height: 1.5; color: #555;">إذا واجهت مشكلة في النقر على الزر، يمكنك نسخ ولصق الرابط التالي في متصفحك:</p>
      <p style="font-size: 14px; line-height: 1.5; color: #777; word-break: break-all;">${verificationUrl}</p>
      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #777; font-size: 12px;">
        <p>أكاديمية د. ناهد باشطح</p>
      </div>
    </div>
  `
}

/**
 * Generate a random token for email verification
 */
function generateVerificationToken(length = 32) {
  const chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
  let token = ""

  for (let i = 0; i < length; i++) {
    token += chars.charAt(Math.floor(Math.random() * chars.length))
  }

  return token
}

// ==================================================================================
// دوال البريد الإلكتروني للاستشارات
// ==================================================================================

type ConsultationData = {
  name: string
  email: string
  phone: string
  message: string
  createdAt: Date
}

/**
 * إرسال بريد تأكيد للمستخدم بعد طلب الاستشارة
 */
export async function sendConsultationConfirmationEmail(
  consultationData: ConsultationData
) {
  try {
    const transporter = createTransporter()

    const emailHtml = generateConsultationConfirmationTemplate({
      userName: consultationData.name,
      responseTime: consultationResponseTimeHours,
    })

    const info = await transporter.sendMail({
      to: consultationData.email,
      html: emailHtml,
      subject: `تأكيد طلب الاستشارة - ${siteName}`,
      from: process.env.SMTP_FROM || "<EMAIL>",
    })

    return { isSended: true, info }
  } catch (error) {
    return { isSended: false, error }
  }
}

/**
 * إرسال بريد إشعار للإدارة بطلب استشارة جديد
 */
export async function sendConsultationNotificationEmail(
  consultationData: ConsultationData,
  adminEmail: string
) {
  try {
    const transporter = createTransporter()

    const emailHtml = generateConsultationNotificationTemplate(consultationData)

    const info = await transporter.sendMail({
      to: adminEmail,
      html: emailHtml,
      subject: `طلب استشارة جديد من ${consultationData.name} - ${siteName}`,
      from: process.env.SMTP_FROM || "<EMAIL>",
    })

    return { isSended: true, info }
  } catch (error) {
    return { isSended: false, error }
  }
}

// ==================================================================================
// قوالب البريد الإلكتروني للاستشارات
// ==================================================================================

/**
 * قالب بريد تأكيد الاستشارة للمستخدم
 */
function generateConsultationConfirmationTemplate({
  userName,
  responseTime,
}: {
  userName: string
  responseTime: number
}) {
  return `
    <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h1 style="color: #333; text-align: center; margin-bottom: 30px;">تأكيد طلب الاستشارة</h1>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
        <h2 style="color: #28a745; margin-top: 0;">✅ تم استلام طلبك بنجاح</h2>
      </div>

      <p style="font-size: 16px; line-height: 1.6; color: #555;">عزيزنا العميل (${userName})،</p>

      <p style="font-size: 16px; line-height: 1.6; color: #555;">
        شكراً لك على ثقتك في خدماتنا. لقد تم استلام طلب الاستشارة الخاص بك بنجاح.
      </p>

      <div style="background-color: #e3f2fd; padding: 15px; border-right: 4px solid #2196f3; margin: 20px 0;">
        <p style="margin: 0; font-size: 16px; color: #1976d2; font-weight: bold;">
          📞 سيتم التواصل معك خلال ${responseTime} ساعة القادمة
        </p>
      </div>

      <h3 style="color: #333; margin-top: 30px;">ماذا يحدث الآن؟</h3>
      <ul style="font-size: 15px; line-height: 1.6; color: #555; padding-right: 20px;">
        <li style="margin-bottom: 8px;">سيقوم فريقنا المختص بمراجعة طلبك</li>
        <li style="margin-bottom: 8px;">سنتواصل معك لتحديد موعد مناسب للاستشارة</li>
        <li style="margin-bottom: 8px;">سنقوم بتوضيح تفاصيل الجلسة وآلية العمل</li>
      </ul>

      <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
        <p style="margin: 0; font-size: 14px; color: #856404;">
          <strong>ملاحظة:</strong> يرجى التأكد من أن هاتفك متاح لاستقبال المكالمات خلال الفترة المحددة.
        </p>
      </div>

      <p style="font-size: 16px; line-height: 1.6; color: #555;">
        إذا كان لديك أي استفسارات إضافية، لا تتردد في التواصل معنا.
      </p>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #777; font-size: 12px;">
        <p style="margin: 5px 0;">${siteName}</p>
        <p style="margin: 5px 0;">نحن هنا لمساعدتك على تحقيق التوازن والسلام الداخلي</p>
      </div>
    </div>
  `
}

/**
 * قالب بريد إشعار الإدارة بطلب استشارة جديد
 */
function generateConsultationNotificationTemplate(
  consultationData: ConsultationData
) {
  const formattedDate = consultationData.createdAt.toLocaleDateString("ar-SA", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  })

  return `
    <div dir="rtl" style="font-family: Arial, sans-serif; max-width: 700px; margin: 0 auto; padding: 20px; border: 1px solid #e0e0e0; border-radius: 5px;">
      <h1 style="color: #333; text-align: center; margin-bottom: 30px;">طلب استشارة جديد</h1>

      <div style="background-color: #fff3cd; padding: 15px; border-radius: 5px; margin-bottom: 20px; border-right: 4px solid #ffc107;">
        <h2 style="color: #856404; margin-top: 0;">🔔 تنبيه: طلب استشارة جديد يتطلب المتابعة</h2>
      </div>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 15px;">تفاصيل الطلب:</h3>

        <table style="width: 100%; border-collapse: collapse;">
          <tr style="border-bottom: 1px solid #dee2e6;">
            <td style="padding: 10px; font-weight: bold; color: #495057; width: 30%;">الاسم:</td>
            <td style="padding: 10px; color: #212529;">${consultationData.name}</td>
          </tr>
          <tr style="border-bottom: 1px solid #dee2e6;">
            <td style="padding: 10px; font-weight: bold; color: #495057;">البريد الإلكتروني:</td>
            <td style="padding: 10px; color: #212529;">
              <a href="mailto:${consultationData.email}" style="color: #007bff; text-decoration: none;">
                ${consultationData.email}
              </a>
            </td>
          </tr>
          <tr style="border-bottom: 1px solid #dee2e6;">
            <td style="padding: 10px; font-weight: bold; color: #495057;">رقم الهاتف:</td>
            <td style="padding: 10px; color: #212529;">
              <a href="tel:${consultationData.phone}" style="color: #007bff; text-decoration: none;">
                ${consultationData.phone}
              </a>
            </td>
          </tr>
          <tr style="border-bottom: 1px solid #dee2e6;">
            <td style="padding: 10px; font-weight: bold; color: #495057;">تاريخ الطلب:</td>
            <td style="padding: 10px; color: #212529;">${formattedDate}</td>
          </tr>
        </table>
      </div>

      <div style="background-color: #f8f9fa; padding: 20px; border-radius: 5px; margin-bottom: 20px;">
        <h3 style="color: #333; margin-top: 0; margin-bottom: 15px;">رسالة العميل:</h3>
        <div style="background-color: white; padding: 15px; border-radius: 5px; border: 1px solid #dee2e6;">
          <p style="margin: 0; line-height: 1.6; color: #212529; white-space: pre-wrap;">${consultationData.message}</p>
        </div>
      </div>

      <div style="background-color: #d1ecf1; padding: 15px; border-radius: 5px; margin: 20px 0; border-right: 4px solid #17a2b8;">
        <p style="margin: 0; font-size: 14px; color: #0c5460;">
          <strong>تذكير:</strong> يجب التواصل مع العميل خلال ${consultationResponseTimeHours} ساعة من وقت الطلب.
        </p>
      </div>

      <div style="text-align: center; margin: 30px 0;">
        <p style="font-size: 14px; color: #6c757d; margin-bottom: 15px;">إجراءات سريعة:</p>
        <a href="mailto:${consultationData.email}" style="background-color: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 0 5px; display: inline-block;">
          📧 إرسال بريد إلكتروني
        </a>
        <a href="tel:${consultationData.phone}" style="background-color: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 4px; margin: 0 5px; display: inline-block;">
          📞 اتصال مباشر
        </a>
      </div>

      <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #e0e0e0; text-align: center; color: #777; font-size: 12px;">
        <p style="margin: 5px 0;">${siteName} - نظام إدارة الاستشارات</p>
        <p style="margin: 5px 0;">تم إرسال هذا البريد تلقائياً من نظام إدارة الموقع</p>
      </div>
    </div>
  `
}
