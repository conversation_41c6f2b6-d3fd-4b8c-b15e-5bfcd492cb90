import { Suspense } from "react"
import Image from "next/image"
import { redirect } from "next/navigation"
import { formatDate } from "@/utils/formaters"
import { Prisma } from "@prisma/client"
import { BotMessageSquare, ChevronLeft } from "lucide-react"

import { verifySession } from "@/lib/auth"
import BadgePrice from "@/components/ui/badge-price"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"

import { UnsubscribeDialog } from "../_components/unsubscribe-dialog"
import { getUserBookOrders } from "../_lib/querys"

export default function UserSubscribedBooksPage() {
  return (
    <div>
      <h1 className="text-2xl font-semibold">اشتراكات الكتب</h1>
      <p className="text-muted mt-2 text-sm">
        هنا يمكنك مشاهدة جميع الكتب التي اشتركت بشكل مجاني او مدفوع
      </p>
      <hr className="border-muted/50 my-6 border-dashed" />
      <Suspense fallback={<SubscribedBooksSkeleton />}>
        <SubscribedBooks />
      </Suspense>
    </div>
  )
}

async function SubscribedBooks() {
  const session = await verifySession()
  if (!session) redirect("/auth/login")

  const userBookOrders = await getUserBookOrders(session?.id)

  if (!userBookOrders.length) {
    return (
      <div className="border-muted/50 bg-muted/5 flex w-full flex-col items-center justify-center gap-2 rounded-xl border border-dashed px-3 py-6 text-center text-balance text-zinc-400">
        <BotMessageSquare className="size-28" />
        <span className="text-xl font-semibold">
          ليس لديك اشتراكات حتا الآن
        </span>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 place-items-center gap-y-6 sm:grid-cols-2 sm:gap-3 md:grid-cols-1 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
      {userBookOrders.map((order) => (
        <SubscribedBookCard key={order.id} bookOrder={order} />
      ))}
    </div>
  )
}

async function SubscribedBookCard({
  bookOrder,
}: {
  bookOrder: Prisma.BookOrderGetPayload<{ include: { book: true } }>
}) {
  const book = bookOrder.book

  return (
    <div className="border-muted/10 flex w-full max-w-80 flex-col items-center gap-6 rounded-xl border bg-white p-4 shadow-lg">
      <div className="bg-muted relative aspect-square w-full overflow-clip rounded-lg border shadow-xs">
        <Image
          src={book.coverImage}
          alt={book.title}
          fill
          className="bg-muted h-auto object-cover"
          sizes="(max-width: 640px) 80vw, 40vw"
        />
      </div>
      <p className="text-muted w-full text-center text-xl font-semibold text-balance">
        {book.title}
      </p>
      <div className="border-muted/10 *:bg-muted/5 *:text-muted grid w-full grid-cols-5 gap-x-1 gap-y-1 rounded-lg border bg-white p-3 shadow-xs *:flex *:items-center *:gap-1 *:rounded-sm *:px-3 *:py-1 *:text-xs *:text-nowrap [&_span]:text-xs [&_span]:font-medium [&_svg]:mr-auto [&_svg]:size-4 [&_svg]:shrink-0">
        <p className="col-span-3">
          سعر الاشتراك <ChevronLeft />
        </p>
        <p className="col-span-2">
          <BadgePrice price={bookOrder.price} />
        </p>
        <p className="col-span-3">
          تاريخ الاشتراك <ChevronLeft />
        </p>
        <p className="col-span-2">{formatDate(bookOrder.createdAt)}</p>
        <p className="col-span-3">
          عدد الصفحات <ChevronLeft />
        </p>
        <p className="col-span-2">{book.pagesCount}</p>
      </div>
      <div className="flex w-full flex-col gap-3">
        <UnsubscribeDialog entity="bookOrders" orderId={bookOrder.id}>
          <Button variant="outline">إلغاء الاشتراك</Button>
        </UnsubscribeDialog>
      </div>
    </div>
  )
}

function SubscribedBooksSkeleton() {
  const array = Array.from({ length: 3 })

  return (
    <div className="grid grid-cols-1 place-content-center place-items-center gap-y-6 sm:grid-cols-2 sm:gap-3 md:grid-cols-1 lg:grid-cols-2 lg:gap-8 xl:grid-cols-3">
      {array.map((_, i) => (
        <Skeleton key={i} className="h-[550px] w-full max-w-72 rounded-xl" />
      ))}
    </div>
  )
}
