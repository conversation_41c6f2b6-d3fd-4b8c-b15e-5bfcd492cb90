import { Suspense } from "react"
import { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { formatDate } from "@/utils/formaters"
import { getInterviews } from "@/lib/querys"
import { Interview } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import { Pagination } from "@/components/ui/pagination"
import { Skeleton } from "@/components/ui/skeleton"

type InterviewsPageProps = { searchParams: Promise<{ page?: string }> }

export const metadata: Metadata = {
  title: `مقابلات د. ناهد باشطح`,
  description: "مقابلات الدكتورة ناهد باشطح",
  keywords: "كتب, ناهد باشطح, العلاج, مؤلفات",
}

// ==================================================================================
// صفحة تعرض كل المقابات
// ==================================================================================

export default async function InterviewsPage(props: InterviewsPageProps) {
  const pageIndex = (await props.searchParams).page || "1"

  return (
    <>
      <Suspense key={pageIndex} fallback={<InterviewsPageSkeleton />}>
        <InterviewsList pageIndex={pageIndex} />
      </Suspense>
    </>
  )
}

async function InterviewsList(props: { pageIndex: string }) {
  const pageIndex = Number(props.pageIndex)

  const { data, pagination } = await getInterviews({ pageIndex })

  if (!data || !data.length) return <div>لم يتم إضافة اي محتويات</div>

  return (
    <>
      {data.map((interview) => (
        <InterviewCard key={interview.id} interview={interview} />
      ))}
      <Pagination url="/interviews" {...pagination} />
    </>
  )
}

// ==================================================================================
// مكون بطاقة المقابلة
// ==================================================================================
function InterviewCard({ interview }: { interview: Interview }) {
  return (
    <div className="w-full space-y-5 overflow-clip rounded-md bg-white shadow-[0_0_15px_-2px_#00000040]">
      <div className="flex flex-col gap-3 p-3 pb-7 sm:flex-row md:py-5">
        <div className="bg-secondary space-y-2 rounded-md p-2 shadow-md">
          <div className="border-muted/0 bg-muted/50 relative aspect-video w-full shrink-0 overflow-clip rounded-md border shadow-md sm:w-40">
            <Image
              sizes="(max-width: 768px) 80vw, 10vw"
              src={interview.thumbnail}
              className="h-auto w-full object-cover"
              alt={interview.title}
              fill
            />
          </div>
          <div className="text-background/70 flex items-center gap-1 text-xs">
            <CalendarDays className="text-background/60 mb-0.5 size-4" />{" "}
            {formatDate(interview.createdAt)}
          </div>
        </div>
        <div className="space-y-2 md:space-y-3">
          <Link
            href={`/interviews/${interview.id}#pagination`}
            className="text-primary hover:text-primary/90 text-lg font-bold underline"
          >
            {interview.title}
          </Link>
          <p className="line-clamp-3 text-sm">{interview.description}</p>
        </div>
      </div>
    </div>
  )
}

function InterviewsPageSkeleton() {
  const array = Array.from({ length: 4 })

  return array.map((_, i) => (
    <Skeleton key={i} className="h-43 w-full rounded-md" />
  ))
}
