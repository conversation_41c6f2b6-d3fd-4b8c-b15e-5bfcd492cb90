import { revalidatePath } from "next/cache"

import prisma from "@/lib/prisma"
import FaqForm from "@/components/dashboard-workspace/faq-CRUD/FaqForm"
import { faqSchema } from "@/components/dashboard-workspace/faq-CRUD/FaqSchema"
import PageLayout from "@/components/dashboard-workspace/PageLayout"

export default function CreateFaqPage() {
  return (
    <PageLayout
      title="إضافة سؤال جديد"
      description="إملأ الحقول بالقيم المناسبة لإنشاء سؤال شائع جديد"
    >
      <FaqForm
        mode="create"
        onAction={async ({ data: request }) => {
          "use server"

          const { success, data, error } = faqSchema
            .omit({ id: true })
            .safeParse(request)
          if (!success)
            return { success: false, errors: error.formErrors.fieldErrors }

          try {
            await prisma.faq.create({ data })

            revalidatePath("/faq")
            revalidatePath("/admin/faq")

            return { success: true, message: "تم إضافة السؤال بنجاح" }
          } catch (error) {
            console.error("Error creating FAQ:", error)
            return { success: false, message: "حدث خطأ أثناء إضافة السؤال" }
          }
        }}
      />
    </PageLayout>
  )
}
