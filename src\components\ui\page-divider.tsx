import React from "react"
import { ArrowDownNarrowWide } from "lucide-react"

export default function PageDivider({
  menuItems,
  pageContents,
  titleMenu,
  comments,
}: {
  titleMenu: string
  menuItems: React.ReactNode
  pageContents: React.ReactNode
  comments?: React.ReactNode
}) {
  return (
    <div className="mt-7 grid grid-cols-1 gap-24 md:grid-cols-3 md:gap-3 lg:gap-6">
      <div className="col-span-1 leading-relaxed md:col-span-2">
        {pageContents}
      </div>
      <div className="h-full md:row-span-2">
        <div className="bg-secondary text-background sticky top-20 w-full overflow-clip rounded-lg">
          <span className="text-background/75 flex items-center gap-2 px-3 py-4 text-xs">
            <ArrowDownNarrowWide className="size-4" /> {titleMenu}
          </span>
          <div className="h-svh max-h-[calc(100vh-360px)] w-full snap-y overflow-y-auto px-3 pb-7 [scrollbar-color:#fafafa_transparent] sm:max-h-[calc(100vh-100px)] md:max-h-[calc(100vh-140px)]">
            {menuItems}
          </div>
          <div className="from-secondary pointer-events-none absolute right-0 bottom-0 left-0 h-10 bg-linear-to-t"></div>
        </div>
      </div>
      {/* التعليقات */}
      <div className="overflow-x-clip md:col-span-2">{comments}</div>
    </div>
  )
}
