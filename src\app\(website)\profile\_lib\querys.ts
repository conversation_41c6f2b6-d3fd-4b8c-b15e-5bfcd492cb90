import "server-only"

import { cache } from "react"

import prisma from "@/lib/prisma"

export const getUserCourseOrders = cache(async (userId: string) => {
  return await prisma.courseOrder.findMany({
    where: { userId, status: "PAID" },
    include: { course: true },
  })
})

export const getUserBookOrders = cache(async (userId: string) => {
  return await prisma.bookOrder.findMany({
    where: { userId, status: "PAID" },
    include: { book: true },
  })
})
