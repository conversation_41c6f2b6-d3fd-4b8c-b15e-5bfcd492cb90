"use client"

import { formatDate } from "@/utils/formaters"
import { Interview } from "@prisma/client"

import DataTable from "@/components/dashboard/components/table/DataTable"
import { DataTableOnDeleteFnDef } from "@/components/dashboard/components/table/types"

import PageLayout from "../PageLayout"
import TableArray from "../TableArray"
import TableFile from "../TableFile"
import TableImage from "../TableImage"

export default function InterviewsTable({
  data,
  onDelete,
}: {
  data: Interview[]
  onDelete?: DataTableOnDeleteFnDef<Interview>
}) {
  return (
    <PageLayout title="المقابلات">
      <DataTable<Interview>
        data={data}
        columnSearch={{ columnKey: "title" }}
        createDataButton={{
          href: "/admin/interviews/create",
          label: "اضافة مقابلة",
        }}
        rowActions={{
          onDelete,
          links: {
            items: [{ basePath: "/admin/interviews/update", label: "تعديل" }],
          },
        }}
        createColumns={[
          { accessorKey: "title", columnLabel: "عنوان المقابلة" },

          {
            accessorKey: "videoUrl",
            columnLabel: "الفيديو",
            cell: ({ value }) => <TableFile fileType="video" href={value} />,
          },
          {
            accessorKey: "thumbnail",
            columnLabel: "صورة مصغرة",
            cell: ({ value }) => <TableImage src={value} />,
          },
          {
            accessorKey: "createdAt",
            columnLabel: "تاريخ الإضافة",
            cell: ({ value }) => formatDate(value),
          },
          { accessorKey: "description", columnLabel: "وصف المقابلة" },

          { accessorKey: "seoDescription", columnLabel: "وصف محركات البحث" },
          {
            accessorKey: "seokeywords",
            columnLabel: "الكلمات المفتاحية",
            cell: ({ value }) => <TableArray arr={value} />,
          },
        ]}
      />
    </PageLayout>
  )
}
