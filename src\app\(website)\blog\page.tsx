import { Suspense } from "react"
import { Metada<PERSON> } from "next"
import Image from "next/image"
import Link from "next/link"
import { formatDate } from "@/utils/formaters"
import { getBlogPosts } from "@/lib/querys"
import { siteName } from "@/configs/site"
import { BlogPost } from "@prisma/client"
import { CalendarDays } from "lucide-react"

import { DividerRedBottom, DividerRedTop } from "@/components/ui/dividers"
import { Pagination } from "@/components/ui/pagination"
import TitleSection from "@/components/ui/title-section"

type BlogPageProps = {
  searchParams: Promise<{ page?: string }>
}

export const metadata: Metadata = {
  title: `المدونة | ${siteName}`,
  description: "مدونة الدكتورة ناهد باشطح",
  keywords: "مدونة, ناهد باشطح, العلاج الشعوري, تقنيات العلاج الشعوري",
}

export default async function BlogPage(props: BlogPageProps) {
  const pageIndex = (await props.searchParams).page || "1"

  return (
    <div className="min-h-svh pt-16">
      <HeaderPages />
      <Suspense key={pageIndex} fallback={<div>جاري التحميل ...</div>}>
        <PostsList pageIndex={pageIndex} />
      </Suspense>
    </div>
  )
}

async function PostsList(props: { pageIndex: string }) {
  const pageIndex = Number(props.pageIndex)

  const { data, pagination } = await getBlogPosts({ pageIndex })
  const lastPost = data?.[0]

  return data && data.length ? (
    <div className="scroll-mt-10 px-4" id="pagination">
      {pageIndex === 1 && (
        <div>
          <Divider label="احدث منشور" />
          <LastPostCard post={lastPost} />
        </div>
      )}
      <Divider label="منشورات سابقة" />
      <div className="mb-6 grid grid-cols-1 place-content-center gap-3 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
        {data?.map((post, index) => {
          if (!index && pageIndex === 1) return null
          return <PostCard key={post.id} post={post} />
        })}
      </div>
      <div className="mb-28">
        <Pagination url="/blog" {...pagination} />
      </div>
    </div>
  ) : (
    <div className="flex min-h-svh w-full items-center justify-center pt-16 text-2xl font-semibold">
      لا توجد منشورات حتي الآن
    </div>
  )
}

function HeaderPages() {
  return (
    <div className="bg-secondary relative py-32">
      <DividerRedTop />
      <TitleSection
        title={<h1>المـــدونـــة</h1>}
        classTitle="text-background"
      />
      <DividerRedBottom />
    </div>
  )
}

function Divider({ label }: { label: string }) {
  return (
    <div className="mt-16 mb-3 flex w-full flex-nowrap items-center gap-4 px-2">
      <h2 id="lectures" className="scroll-mt-20">
        {label}
      </h2>
      <hr className="mt-1.5 flex-1" />
    </div>
  )
}

function LastPostCard({ post }: { post?: BlogPost }) {
  if (!post) return null

  return (
    <div className="border-muted/5 grid grid-cols-1 gap-3 rounded-xl border bg-white p-2 md:grid-cols-2">
      <Link
        href={`/blog/${post.id}`}
        className="group w-full space-y-3 underline underline-offset-6"
      >
        <div className="bg-muted relative aspect-640/426 w-full overflow-clip rounded-t-lg rounded-b-sm md:rounded-l-sm md:rounded-r-lg">
          <Image
            src={post.image}
            className="scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
            sizes="(max-width: 640px) 90vw, 50vw"
            alt={post.title}
            fill
          />
        </div>
        <p className="text-xl leading-relaxed font-semibold md:hidden">
          {post.title}
        </p>
      </Link>
      <div className="md:px-6 md:pt-3">
        <Link
          href={`/blog/${post.id}`}
          className="mb-3 hidden text-3xl leading-relaxed font-semibold underline underline-offset-10 md:mb-6 md:flex"
        >
          {post.title}
        </Link>
        <p className="text-muted mb-3 line-clamp-2 leading-relaxed md:mb-6 md:line-clamp-4 md:text-balance">
          {post.seoDescription}
        </p>
        <p className="text-muted flex flex-nowrap items-center gap-1 text-sm md:text-base">
          <CalendarDays className="mb-1 size-4 md:size-5" />{" "}
          {formatDate(post.createdAt)}
        </p>
      </div>
    </div>
  )
}

function PostCard({ post }: { post: BlogPost }) {
  return (
    <div className="border-muted/5 space-y-3 rounded-xl border bg-white p-2">
      <Link
        href={`/blog/${post.id}`}
        className="group space-y-3 underline underline-offset-6"
      >
        <div className="bg-muted relative aspect-640/426 w-full overflow-clip rounded-t-lg rounded-b-sm">
          <Image
            src={post.image}
            className="scale-105 object-cover transition-all duration-300 ease-out group-hover:scale-110"
            sizes="(max-width: 640px) 90vw, 50vw"
            alt={post.title}
            fill
          />
        </div>
        <p className="text-xl font-semibold">{post.title}</p>
      </Link>
      <p className="text-muted mt-3 line-clamp-2">{post.seoDescription}</p>
      <p className="text-muted flex flex-nowrap items-center gap-1 text-sm">
        <CalendarDays className="mb-1 size-4" /> {formatDate(post.createdAt)}
      </p>
    </div>
  )
}
