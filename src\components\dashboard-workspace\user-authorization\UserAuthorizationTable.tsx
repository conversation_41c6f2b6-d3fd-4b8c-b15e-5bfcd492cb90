"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeaderCell,
  TableRow,
} from "@/components/dashboard/components/ui/Table";
import { Badge } from "@/components/dashboard/components/ui/Badge";
import { Button } from "@/components/dashboard/components/ui/Button";

type User = {
  id: string;
  name: string;
  email: string;
  courseOrders: {
    id: string;
    courseId: string;
    course: {
      id: string;
      title: string;
      price: number;
    };
  }[];
};

type UserAuthorizationTableProps = {
  users: User[];
  selectedUser: string | null;
  onSelectUser: (userId: string) => void;
};

export default function UserAuthorizationTable({ users, selectedUser, onSelectUser }: UserAuthorizationTableProps) {
  return (
    <div className="w-full overflow-x-auto rounded-md border">
      <Table className="">
        <TableHead>
          <TableRow>
            <TableHeaderCell>الاسم</TableHeaderCell>
            <TableHeaderCell>البريد الإلكتروني</TableHeaderCell>
            <TableHeaderCell>الدورات تدريبية المصرح بها</TableHeaderCell>
            <TableHeaderCell>الإجراءات</TableHeaderCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id} className={selectedUser === user.id ? "bg-gray-100 dark:bg-gray-800" : ""}>
              <TableCell>{user.name}</TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>
                <div className="flex flex-wrap gap-1">
                  {user.courseOrders.length > 0 ? (
                    user.courseOrders.map((order) => (
                      <Badge key={order.id} variant="success">
                        {order.course.title}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-gray-500">لا يوجد</span>
                  )}
                </div>
              </TableCell>
              <TableCell>
                <Button
                  onClick={() => onSelectUser(user.id)}
                  variant={selectedUser === user.id ? "light" : "secondary"}
                >
                  {selectedUser === user.id ? "تم التحديد" : "تحديد"}
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
