import { revalidatePath, revalidateTag } from "next/cache"
import { dataLimits } from "@/configs/site"

import prisma from "@/lib/prisma"
import BlogPostForm from "@/components/dashboard-workspace/blogPosts-CRUD/BlogPostsForm"
import { blogPostSchema } from "@/components/dashboard-workspace/blogPosts-CRUD/blogPostsSchema"
import PageLayout from "@/components/dashboard-workspace/PageLayout"
import { handleFileUpdate } from "@/components/dashboard/components/fileUploader/handleFiles"

export default function CreateBlogPostPage() {
  return (
    <PageLayout title="إضافة منشور جديد">
      <BlogPostForm
        mode="create"
        onAction={async ({ data: request }) => {
          "use server"

          const { success, data, error } = blogPostSchema
            .omit({ id: true })
            .safeParse(request)
          if (!success)
            return { success: false, errors: error.formErrors.fieldErrors }

          const image = await handleFileUpdate("", data.image, "blog/image/")

          const [totalCount] = await prisma.$transaction([
            prisma.blogPost.count(),
            prisma.blogPost.create({ data: { ...data, image } }),
          ])

          const totalPages = Math.ceil(totalCount / dataLimits.blogPost)

          revalidatePath("/admin/blogPosts")
          revalidateTag("blogPagesCount")
          revalidateTag("pageTitlesAndIds")

          for (let page = 1; page <= totalPages; page++)
            revalidatePath(`/blog/${page}`)

          return { success: true, message: "تم اضافة المنشور بنجاح" }
        }}
      />
    </PageLayout>
  )
}
